from odoo import api, fields, models, registry, SUPERUSER_ID
import kombu
import logging
import socket
import json
import threading
import time
from datetime import datetime

_logger = logging.getLogger(__name__)

class RabbitmqServer(models.Model):
    _name = "rabbitmq.server"
    _description = "RabbitMQ Server Configuration (Kombu Version)"

    url = fields.Char("MQ Single URL",)
    name = fields.Char("Service Name", required=True, default="RabbitMQ Service")
    host = fields.Char("Server Address", required=True)
    user = fields.Char("Username", required=True)
    password = fields.Char("Password", required=True)
    port = fields.Integer("Port", required=True, default=5672)
    vhost = fields.Char("Virtual Host", default="/")
    queue_name = fields.Char("Default Queue", default="MQ_CLIMAS_ODOO_ROUTE_KEY")
    auto_start = fields.Boolean("Auto-Start Consumer", default=True)
    
    # New fields for connection status
    connection_status = fields.Selection([
        ('disconnected', 'Disconnected'),
        ('connected', 'Connected'),
        ('error', 'Error')
    ], string="Connection Status", default='disconnected', readonly=True)
    last_connection_check = fields.Datetime("Last Connection Check")
    connection_error = fields.Text("Connection Error", readonly=True)
    consumer_running = fields.Boolean("Consumer Running", default=False, readonly=True)

    def get_connection(self):
        """Create Kombu connection with Odoo-safe parameters"""
        if self.url:
            return kombu.Connection(self.url)
        else:
            return kombu.Connection(
                hostname=self.host,
                port=self.port,
                userid=self.user,
                password=self.password,
                virtual_host=self.vhost,
                ssl=False
            )

    def check_connection(self):
        """Check if connection to RabbitMQ is possible"""
        self.ensure_one()
        try:
            with self.get_connection() as conn:
                conn.connect()
                self.write({
                    'connection_status': 'connected',
                    'last_connection_check': fields.Datetime.now(),
                    'connection_error': False
                })
                return True
        except Exception as e:
            self.write({
                'connection_status': 'error',
                'last_connection_check': fields.Datetime.now(),
                'connection_error': str(e)
            })
            _logger.error(f"RabbitMQ connection check failed: {e}")
            return False

    def publish_message(self, body, queue_name=None, exchange=""):
        """Publish message using Kombu"""
        queue_name = queue_name or self.queue_name
        try:
            with self.get_connection() as conn:
                producer = conn.Producer()
                queue = kombu.Queue(
                    name=queue_name,
                    durable=True,
                    channel=conn.default_channel
                )
                queue.declare()
                producer.publish(
                    body,
                    exchange=exchange,
                    routing_key=queue_name,
                    serializer='json'
                )
            # Update connection status on successful publish
            self.write({
                'connection_status': 'connected',
                'last_connection_check': fields.Datetime.now(),
                'connection_error': False
            })
        except Exception as e:
            self.write({
                'connection_status': 'error',
                'last_connection_check': fields.Datetime.now(),
                'connection_error': str(e)
            })
            _logger.error(f"Publish error: {e}")
            raise
        
        
    def _message_handler(self, body, message):
        """Event handler with dynamic method routing"""
        dbname = self.env.cr.dbname
        
        try:
            with registry(dbname).cursor() as cr:
                env = api.Environment(cr, SUPERUSER_ID, {})
                
                try:
                    # Parse message content
                    msg_content = json.loads(body) if isinstance(body, str) else body
                    event = msg_content.get('event')
                    body_data = msg_content.get('body', {})
                    
                    _logger.info(f"Processing event: {event}")
                    
                    # Update connection status on successful message receipt
                    try:
                        server = env['rabbitmq.server'].browse(self.id)
                        server.write({
                            'connection_status': 'connected',
                            'last_connection_check': fields.Datetime.now(),
                            'connection_error': False
                        })
                        cr.commit()
                    except Exception as status_error:
                        _logger.warning(f"Failed to update connection status: {status_error}")
                    
                    # Event handlers mapping
                    event_handlers = {
                        'DILEMMA_RESULT': {
                            'model': 'cep.ag.result',
                            'method': 'save_dillemas'
                        },
                        'AGENDA_RESULT': {
                            'model': 'cep.ag.result',
                            'method': 'save_agendas'
                        },
                        'PDF_REFERENCE_RESULT': {
                            'model': 'cep.ag.result',
                            'method': 'save_dilemma_reference'
                        },
                        'FAILED_RESULT': {
                            'model': 'cep.ag.project',
                            'method': 'save_failed_message'
                        },
                        'KEYWORD_FREQUENCY_RESULT': {
                            'model': 'cep.ag.result',
                            'method': 'save_frequency_analysis'
                        },
                        'NGRAM_RESULT': {
                            'model': 'cep.ag.result',
                            'method': 'save_n_gram'
                        },
                    }

                    if event in event_handlers:
                        handler = event_handlers[event]
                        model = env[handler['model']]
                        
                        if hasattr(model, handler['method']):
                            method = getattr(model, handler['method'])
                            method(body=body_data)
                            cr.commit()  # Commit after successful processing
                            _logger.info(f"Successfully processed {event}")
                        else:
                            _logger.error(f"Method {handler['method']} not found in {handler['model']}")
                            raise Exception(f"Handler method not found: {handler['method']}")
                    else:
                        _logger.warning(f"No handler for event: {event}")
                        raise Exception(f"Unknown event type: {event}")
                        
                except json.JSONDecodeError as e:
                    _logger.error(f"Invalid JSON in message: {str(e)}")
                    raise
                except Exception as e:
                    _logger.error(f"Error processing message: {str(e)}")
                    cr.rollback()  # Rollback on error
                    raise
                    
        except Exception as e:
            _logger.error(f"Database error in message handler: {str(e)}")
            raise

    def _start_consumer(self):
        """Start consumer thread with heartbeat and reconnection handling"""
        params = {
            "url": self.url,
            'host': self.host,
            'port': self.port,
            'user': self.user,
            'password': self.password,
            'vhost': self.vhost,
            'queue_name': self.queue_name,
            'dbname': self.env.cr.dbname,
            'server_id': self.id
        }

        def create_connection():
            """Helper function to create a new connection"""
            if params['url']:
                return kombu.Connection(
                    params['url'],
                    heartbeat=60
                )
            return kombu.Connection(
                hostname=params['host'],
                port=params['port'],
                userid=params['user'],
                password=params['password'],
                virtual_host=params['vhost'],
                ssl=False,
                heartbeat=60
            )

        def message_callback(body, message):
            """Callback wrapper for message handling"""
            try:
                # Create a new database cursor for this message
                with registry(params['dbname']).cursor() as cr:
                    env = api.Environment(cr, SUPERUSER_ID, {})
                    server = env['rabbitmq.server'].browse(params['server_id'])
                    
                    # Process the message
                    server._message_handler(body, message)
                    
                # Acknowledge message only after successful processing
                message.ack()
                
            except Exception as e:
                _logger.error(f"Error in message callback: {str(e)}")
                # Reject message and requeue for retry
                try:
                    message.reject(requeue=True)
                except:
                    pass

        def consume():
            connection = None
            retry_count = 0
            
            try:
                # Initial setup with separate cursor
                with registry(params['dbname']).cursor() as setup_cr:
                    env = api.Environment(setup_cr, SUPERUSER_ID, {})
                    server = env['rabbitmq.server'].browse(params['server_id'])
                    server.write({'consumer_running': True})
                    setup_cr.commit()
                
                while True:
                    try:
                        # Establish or re-establish connection
                        if connection is None or not connection.connected:
                            if connection:
                                try:
                                    connection.close()
                                except:
                                    pass
                            
                            connection = create_connection()
                            connection.connect()
                            _logger.info(f"Connected to RabbitMQ at {params['host']}:{params['port']}")
                            
                            # Reset retry count on successful connection
                            retry_count = 0
                            
                            # Update connection status
                            try:
                                with registry(params['dbname']).cursor() as status_cr:
                                    env = api.Environment(status_cr, SUPERUSER_ID, {})
                                    server = env['rabbitmq.server'].browse(params['server_id'])
                                    server.write({
                                        'connection_status': 'connected',
                                        'last_connection_check': fields.Datetime.now(),
                                        'connection_error': False
                                    })
                                    status_cr.commit()
                            except Exception as status_error:
                                _logger.warning(f"Failed to update connection status: {status_error}")
                        
                        # Set up queue
                        queue = kombu.Queue(
                            name=params['queue_name'],
                            durable=True,
                            channel=connection.default_channel
                        )
                        
                        # Declare queue to ensure it exists
                        queue.declare()
                        
                        # Configure consumer with error handling
                        with connection.Consumer(
                            queue,
                            callbacks=[message_callback],
                            prefetch_count=1  # Process one message at a time
                        ):
                            _logger.info(f"🚀 Consumer started on queue: {params['queue_name']}")
                            
                            last_status_update = datetime.now()
                            
                            while True:
                                try:
                                    # Check connection and heartbeat
                                    connection.heartbeat_check()
                                    connection.drain_events(timeout=1)
                                    
                                except socket.timeout:
                                    # Normal timeout, continue loop
                                    connection.heartbeat_check()
                                    
                                    # Periodically update status (every 30 seconds)
                                    now = datetime.now()
                                    if (now - last_status_update).seconds >= 30:
                                        try:
                                            with registry(params['dbname']).cursor() as status_cr:
                                                env = api.Environment(status_cr, SUPERUSER_ID, {})
                                                server = env['rabbitmq.server'].browse(params['server_id'])
                                                server.write({
                                                    'connection_status': 'connected',
                                                    'last_connection_check': fields.Datetime.now()
                                                })
                                                status_cr.commit()
                                            last_status_update = now
                                        except Exception as status_error:
                                            _logger.warning(f"Failed to update periodic status: {status_error}")
                                    
                                    continue
                                    
                                except (ConnectionError, socket.error, kombu.exceptions.ConnectionError) as e:
                                    _logger.error(f"Connection lost: {str(e)}")
                                    
                                    # Update status to show error
                                    try:
                                        with registry(params['dbname']).cursor() as error_cr:
                                            env = api.Environment(error_cr, SUPERUSER_ID, {})
                                            server = env['rabbitmq.server'].browse(params['server_id'])
                                            server.write({
                                                'connection_status': 'error',
                                                'last_connection_check': fields.Datetime.now(),
                                                'connection_error': str(e)
                                            })
                                            error_cr.commit()
                                    except Exception as status_error:
                                        _logger.warning(f"Failed to update error status: {status_error}")
                                    
                                    break  # Exit inner loop to reconnect
                                    
                                except Exception as e:
                                    _logger.error(f"Unexpected error in consumer loop: {str(e)}")
                                    
                                    # Update status to show error
                                    try:
                                        with registry(params['dbname']).cursor() as error_cr:
                                            env = api.Environment(error_cr, SUPERUSER_ID, {})
                                            server = env['rabbitmq.server'].browse(params['server_id'])
                                            server.write({
                                                'connection_status': 'error',
                                                'last_connection_check': fields.Datetime.now(),
                                                'connection_error': str(e)
                                            })
                                            error_cr.commit()
                                    except Exception as status_error:
                                        _logger.warning(f"Failed to update error status: {status_error}")
                                    
                                    break

                    except Exception as e:
                        _logger.error(f"Consumer error: {str(e)}")
                        
                        # Close connection safely
                        if connection:
                            try:
                                connection.close()
                            except:
                                pass
                        connection = None
                        
                        # Update status to show error
                        try:
                            with registry(params['dbname']).cursor() as error_cr:
                                env = api.Environment(error_cr, SUPERUSER_ID, {})
                                server = env['rabbitmq.server'].browse(params['server_id'])
                                server.write({
                                    'connection_status': 'error',
                                    'last_connection_check': fields.Datetime.now(),
                                    'connection_error': str(e)
                                })
                                error_cr.commit()
                        except Exception as status_error:
                            _logger.warning(f"Failed to update error status: {status_error}")
                        
                        # Exponential backoff for reconnection
                        retry_count += 1
                        sleep_time = min(30, 2 ** min(5, retry_count))
                        _logger.info(f"Attempting reconnect in {sleep_time} seconds... (retry {retry_count})")
                        time.sleep(sleep_time)
                        
            except Exception as e:
                _logger.error(f"Fatal consumer error: {str(e)}")
            finally:
                # Clean up resources
                if connection:
                    try:
                        connection.close()
                    except:
                        pass
                
                # Mark consumer as stopped
                try:
                    with registry(params['dbname']).cursor() as cleanup_cr:
                        env = api.Environment(cleanup_cr, SUPERUSER_ID, {})
                        server = env['rabbitmq.server'].browse(params['server_id'])
                        server.write({
                            'consumer_running': False,
                            'connection_status': 'disconnected'
                        })
                        cleanup_cr.commit()
                except Exception as cleanup_error:
                    _logger.warning(f"Failed to cleanup consumer state: {cleanup_error}")

        if self.auto_start:
            thread = threading.Thread(target=consume, daemon=True)
            thread.start()
            _logger.info(f"Started RabbitMQ consumer thread for server {self.id}")
        
    def action_run(self):
        """Test RabbitMQ connection and start consumer"""
        self.check_connection()
        self._start_consumer()
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'RabbitMQ Consumer',
                'message': 'Consumer started in background',
                'sticky': False,
                'type': 'success' if self.connection_status == 'connected' else 'warning',
            }
        }
        
    def action_check_connection(self):
        """Manual connection check action for the UI"""
        self.check_connection()
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'RabbitMQ Connection',
                'message': 'Connected successfully' if self.connection_status == 'connected' 
                           else f'Connection error: {self.connection_error}',
                'sticky': False,
                'type': 'success' if self.connection_status == 'connected' else 'warning',
            }
        }
        
    @api.model
    def _cron_check_rabbitmq_connections(self):
        """Cron job to periodically check all RabbitMQ connections and restart consumers if needed"""
        servers = self.search([])
        for server in servers:
            try:
                if server.connection_status != 'connected':
                    _logger.info(f"Connection issue detected for {server.name}, attempting to restart consumer")
                    server._start_consumer()
            except Exception as e:
                _logger.error(f"Error cronjob checking RabbitMQ connection for {server.name}: {e}")