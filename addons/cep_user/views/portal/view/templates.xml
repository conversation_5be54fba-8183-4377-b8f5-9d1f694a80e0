<?xml version="1.0"?>
<odoo>
    <template id="cep_user.portal_my_home" inherit_id="portal.portal_my_home">
        <xpath expr="//t[@t-call='portal.portal_layout']" position="replace">
            <t t-call="portal.portal_layout">
                <t t-set="head">
                    <!-- Include custom assets -->
                    <t t-call-assets="cep_user.cep_user_portal_view" />
                </t>
                <t t-set="my_details" t-value="False" />
                <t t-set="no_breadcrumbs" t-value="True" />

                <div class="o_portal_my_home">

                    <main class="content">
                        <div class="container-fluid p-0">


                            <div class="row ">
                                <div class="col-md-4 col-xl-3">
                                    <div class="card mt-5">
                                        <div class="card-header">
                                            <h5 class="card-title mb-0">Profile Details</h5>
                                        </div>
                                        <div class="card-body text-center">
                                            <img src="/cep_user/static/src/img/user-avatar.jpg"
                                                alt="user logo"
                                                class="img-fluid rounded-circle mb-2" width="128"
                                                height="128" />
                                            <h5 class="card-title mb-0">
                                                <t t-esc="user.name" />
                                            </h5>
                                            <div class="text-muted mb-2">
                                                <div>
                                                    <t
                                                        t-if="user.has_group('base.group_user')">
                                                        Internal User
                                                    </t>
                                                    <t
                                                        t-elif="user.has_group('base.group_portal')">
                                                        Portal User
                                                    </t>
                                                </div>
                                            </div>
                                            <div class="dropdown">
                                                <button class="btn btn-primary" type="button"
                                                    id="dropdownMenuButton"
                                                    data-bs-toggle="dropdown" aria-haspopup="true"
                                                    aria-expanded="false">
                                                    <i class="fa fa-cog"></i>
                                                    <span>Settings</span>
                                                </button>
                                                <div class="dropdown-menu"
                                                    aria-labelledby="dropdownMenuButton">

                                                    <!-- Edit Profile Info -->
                                                    <a href="/my/account"
                                                        class="dropdown-item d-flex align-items-center gap-2">
                                                        <i class="fa fa-pencil"></i>
                                                        <span>Edit Profile</span>
                                                    </a>

                                                    <!-- Edit Security Settings -->
                                                    <a href="/my/security"
                                                        class="dropdown-item d-flex align-items-center gap-2">
                                                        <i class="fa fa-shield"></i>
                                                        <span>Edit Security Settings</span>
                                                    </a>

                                                    <!-- Reset Password -->
                                                    <a href="/my/security"
                                                        class="dropdown-item d-flex align-items-center gap-2">
                                                        <i class="fa fa-undo"></i>
                                                        <span>Reset Password</span>
                                                    </a>

                                                </div>
                                            </div>

                                        </div>

                                        <hr class="my-0" />
                                        <div class="card-body">
                                            <h5 class="h6 card-title">About</h5>
                                            <ul class="list-unstyled mb-0">
                                                <li class="mb-1">
                                                    <span class="fw-bold">
                                                        Company:
                                                    </span>
                                                    <span>
                                                        <t t-esc="user.company_id.name" />
                                                    </span>
                                                </li>
                                                <li class="mb-1">
                                                    <span class="fw-bold">
                                                        Address:
                                                    </span>
                                                    <span>
                                                        <t t-esc="formatted_address" />
                                                    </span>
                                                </li>


                                            </ul>
                                        </div>

                                    </div>
                                </div>

                                <div class="col-md-8 col-xl-9">
                                    <div class="row">
                                        <t
                                            t-if="request.env['ir.model'].sudo().search([('model', '=', 'cep.proposal.proposal')])">
                                            <div class="col-md-4">

                                                <div class="card s_user_profile mt-5">
                                                    <div class="card-body s_user_profile_button">
                                                        <div class="row justify-content-end ">


                                                            <div class="col-auto">
                                                                <div class="stat text-primary">

                                                                    <t t-set="proposal_count"
                                                                        t-value="request.env['cep.proposal.proposal'].sudo().search_count([('owner_id', '=', user.id)])" />
                                                                    <t t-esc="proposal_count" />
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <a href="/my/proposals" class="">

                                                            <div
                                                                class="d-flex flex-column align-items-center gap-2">
                                                                <img
                                                                    src="/cep_user/static/src/img/proposals.png"
                                                                    width="75" height="75" alt="" />
                                                                <h5>Proposals</h5>
                                                            </div>
                                                        </a>

                                                    </div>
                                                </div>


                                            </div>
                                        </t>
                                        <t
                                            t-if="request.env['ir.model'].sudo().search([('model', '=', 'cep.idea.idea')])">
                                            <div class="col-md-4">

                                                <div class="card s_user_profile mt-5">
                                                    <div class="card-body s_user_profile_button">
                                                        <div class="row justify-content-end">


                                                            <div class="col-auto">
                                                                <div class="stat text-primary">
                                                                    <t t-set="idea_count"
                                                                        t-value="request.env['cep.idea.idea'].sudo().search_count([('owner_id', '=', user.id)])" />
                                                                    <t t-esc="idea_count" />

                                                                </div>
                                                            </div>
                                                        </div>
                                                        <a href="/my/ideas" class="">
                                                            <div
                                                                class="d-flex flex-column align-items-center">
                                                                <img
                                                                    src="/cep_user/static/src/img/idea.png"
                                                                    width="75"
                                                                    height="75" alt="" />
                                                                <h5>Ideas</h5>
                                                            </div>
                                                        </a>

                                                    </div>
                                                </div>


                                            </div>
                                        </t>

                                        <t
                                            t-if="request.env['ir.model'].sudo().search([('model', '=', 'cep.ef.doc_review')])">
                                            <div class="col-md-4">

                                                <div class="card s_user_profile mt-5">
                                                    <div class="card-body s_user_profile_button">
                                                        <div class="row justify-content-end">


                                                            <div class="col-auto">
                                                                <div class="stat text-primary">
                                                                    <span id="review-count">
                                                                        0
                                                                    </span>

                                                                </div>
                                                            </div>
                                                        </div>

                                                        <a href="/review-pending" class="">
                                                            <div
                                                                class="d-flex flex-column align-items-center gap-2">
                                                                <img
                                                                    src="/cep_user/static/src/img/pencil.png"
                                                                    width="75" height="75" alt="" />
                                                                <h5>Review Document</h5>
                                                            </div>
                                                        </a>
                                                    </div>
                                                </div>


                                            </div>
                                        </t>

                                    </div>


                                </div>

                            </div>
                        </div>
                    </main>


                </div>
            </t>
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                fetch('/review-pending/count', {
                method: 'GET',
                headers: {
                'Content-Type': 'application/json'
                }
                })
                .then(response => {
                console.log( response)
                return response.json()
                })
                .then(data => {
                document.getElementById('review-count').textContent = data.review_count;
                })
                .catch(error => console.error('Error fetching pending review count:', error));
                });
            </script>
        </xpath>
    </template>
</odoo>