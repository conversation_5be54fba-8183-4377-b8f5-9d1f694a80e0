$(document).ready(function () {
  // Initialize DataTable and assign to 'table' variable
  const table = $('#portal-table').DataTable({
    scrollX: true,
  });

  // Make rows clickable
  $('#portal-table tbody').on('click', 'tr', function () {
    var href = $(this).data('href');

    if (href) {
      window.location.href = href;
    }
  });

  $('.delete-proposal').click(function (e) {
    e.preventDefault();

    const title = $(this).closest('tr').find('.title').text().trim();
    const href = $(this).attr('href');
    const bs_target = $(this).data('bs-target');

    $(bs_target).find('.modal-footer a').attr('href', href);
    $(bs_target).find('.modal-body span').text(title);
  });

  $('#delete-proposal-modal-yes').click(function () {
    $(this).closest('.modal-footer').find('button').click();
  });

  $('.delete-idea').click(function (e) {
    e.preventDefault();

    const title = $(this).closest('tr').find('.title').text().trim();
    const href = $(this).attr('href');
    const bs_target = $(this).data('bs-target');

    $(bs_target).find('.modal-footer a').attr('href', href);
    $(bs_target).find('.modal-body span').text(title);
  });

  $('#delete-idea-modal-yes').click(function () {
    $(this).closest('.modal-footer').find('button').click();
  });

  // Add title search filter
  $('#portal-table_filter input').off().on('keyup', function () {
    table.columns(0).search(this.value).draw();
  });
});
