from odoo import http
from odoo.http import request


def isAuthenticate():
    return request.env.user != request.website.user_id


class UserPortalController(http.Controller):

    @http.route('/my/proposals', type='http', auth='public', website=True)
    def proposl_list(self, **kwargs):
        if not isAuthenticate():
            return request.redirect('/web/login')
        proposals = request.env['cep.proposal.proposal'].sudo().search(
            [('owner_id', '=', request.env.user.id)])
        context = {'proposals': proposals}
        return request.render('cep_user.proposals_list', context)

    @http.route('/my/ideas', type='http', auth='public', website=True)
    def idea_list(self, **kwargs):
        if not isAuthenticate():
            return request.redirect('/web/login')
        ideas = request.env['cep.idea.idea'].sudo().search(
            [('owner_id', '=', request.env.user.id)])
        context = {'ideas': ideas}
        return request.render('cep_user.ideas_list', context)

    @http.route('/my/home', type='http', auth='public', website=True)
    def home(self, **kwargs):
        if not isAuthenticate():
            return request.redirect('/web/login')
        user = request.env.user
        partner = user.partner_id

        # Gather user address components
        address_components = [
            partner.street or '',
            partner.street2 or '',
            partner.city or '',
            partner.state_id.name if partner.state_id else '',
            partner.zip or '',
            partner.country_id.name if partner.country_id else '',
        ]

        # Remove empty components and combine with commas
        formatted_address = ', '.join(
            component for component in address_components if component.strip()
        )

        if not formatted_address:
            formatted_address = 'N/A'
        context = {
            'user': user,
            'formatted_address': formatted_address
        }
        return request.render('cep_user.portal_my_home', context)
