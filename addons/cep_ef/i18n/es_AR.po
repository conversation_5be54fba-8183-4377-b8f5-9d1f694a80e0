# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* cep_ef
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0-20241017\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-09 11:26+0000\n"
"PO-Revision-Date: 2024-12-09 11:26+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.review_pending
msgid "Action"
msgstr "Acción"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__message_needaction
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__message_needaction
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__message_needaction
msgid "Action Needed"
msgstr "Acción necesaria"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_activities
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__activity_ids
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__activity_ids
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__activity_ids
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__activity_ids
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_assembly_info_form
msgid "Activities"
msgstr "Actividades"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__activity_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__activity_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__activity_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews__activity_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__activity_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_surveys__activity_id
#: model:ir.ui.menu,name:cep_ef.cep_ef_activity
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_activity_form
msgid "Activity"
msgstr "Actividad"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__activity_exception_decoration
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__activity_exception_decoration
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoración de excepción de actividad"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__activity_name
msgid "Activity Name"
msgstr "Nombre de la actividad"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__activity_state
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__activity_state
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__activity_state
msgid "Activity State"
msgstr "Estado de actividad"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__activity_type_icon
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__activity_type_icon
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icono de tipo de actividad"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__address
msgid "Address"
msgstr "Dirección"

#. module: cep_ef
#: model:ir.model.fields.selection,name:cep_ef.selection__survey_send_to_wizard__recipient_type__all
msgid "All"
msgstr "Todo"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.submit_review
msgid "Are you sure you want to submit your review ?"
msgstr "¿Está seguro de que desea enviar su reseña?"

#. module: cep_ef
#: model:ir.actions.act_window,name:cep_ef.cep_ef_assembly_info_action
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__assembly_info_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__assembly_info_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_reviewer__assembly_info_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__assembly_info_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__assembly_info_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__assembly_info_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__assembly_info_id
#: model:ir.ui.menu,name:cep_ef.cep_ef_assembly_info
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_assembly_info_form
msgid "Assembly Info"
msgstr "Información de montaje"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_assembly_info_calendar
msgid "Assembly Info Calendar"
msgstr "Calendario de información de montaje"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__assembly_title
msgid "Assembly Title"
msgstr "Título de la Asamblea"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__message_attachment_count
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__message_attachment_count
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__message_attachment_count
msgid "Attachment Count"
msgstr "Recuento de archivos adjuntos"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__background
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__background
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__background
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__background
msgid "Background"
msgstr "Fondo"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__budget
msgid "Budget"
msgstr "Presupuesto"

#. module: cep_ef
#: model:ir.ui.menu,name:cep_ef.cep_ef_root
msgid "CA Evaluation and Monitoring"
msgstr "Evaluación y seguimiento de CA"

#. module: cep_ef
#: model:ir.actions.act_window,name:cep_ef.cep_ef_activities_action
msgid "CEP EF Activities"
msgstr "Actividades del CEP EF"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_activities_search
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_activity_calendar
msgid "CEP EF Activity"
msgstr "Actividad del CEP EF"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_assembly_info
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_assembly_info_search
msgid "CEP EF Assembly Info"
msgstr "Información de la Asamblea CEP EF"

#. module: cep_ef
#: model:ir.actions.act_window,name:cep_ef.cep_ef_criteria_action
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_criteria_search
msgid "CEP EF Criteria"
msgstr "Criterios CEP EF"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_organizations
msgid "CEP EF Organizations"
msgstr "Organizaciones CEP EF"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_phases
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_phases_search
msgid "CEP EF Phase"
msgstr "Fase CEP EF"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.view_survey_send_to_wizard_form
msgid "Cancel"
msgstr "Cancelar"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.submit_review
msgid "Comment"
msgstr "Comentario"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__comments
msgid "Comments"
msgstr "Comentarios"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__commissioners
msgid "Commissioners"
msgstr "Comisionados"

#. module: cep_ef
#: model:ir.model.fields.selection,name:cep_ef.selection__cep_ef_activities__status__completed
#: model:ir.model.fields.selection,name:cep_ef.selection__cep_ef_doc_review__status__completed
#: model:ir.model.fields.selection,name:cep_ef.selection__cep_ef_surveys__status__completed
msgid "Completed"
msgstr "Terminado"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__cover_photo
msgid "Cover Photo"
msgstr "Foto de portada"

#. module: cep_ef
#: model_terms:ir.actions.act_window,help:cep_ef.cep_ef_assembly_info_action
msgid "Create a new Assembly"
msgstr "Crear una nueva asamblea"

#. module: cep_ef
#: model_terms:ir.actions.act_window,help:cep_ef.cep_ef_criteria_action
msgid "Create a new Criteria"
msgstr "Crear un nuevo criterio"

#. module: cep_ef
#: model_terms:ir.actions.act_window,help:cep_ef.cep_ef_phase_action
msgid "Create a new Phase"
msgstr "Crear una nueva fase"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_criteria__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_reviewer__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_phases__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_surveys__create_uid
#: model:ir.model.fields,field_description:cep_ef.field_survey_send_to_wizard__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_criteria__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_reviewer__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_phases__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__create_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_surveys__create_date
#: model:ir.model.fields,field_description:cep_ef.field_survey_send_to_wizard__create_date
msgid "Created on"
msgstr "Creado el"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_criteria
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__criteria_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_criteria__criteria
#: model:ir.ui.menu,name:cep_ef.cep_ef_criteria
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_criteria_form
msgid "Criteria"
msgstr "Criterios"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__deadline
#: model_terms:ir.ui.view,arch_db:cep_ef.review_pending
msgid "Deadline"
msgstr "Fecha límite"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__deliverable_name
msgid "Deliverable Name"
msgstr "Nombre del entregable"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_deliverables
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__deliverable_ids
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_activity_form
msgid "Deliverables"
msgstr "Entregables"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__description
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_criteria__description
msgid "Description"
msgstr "Descripción"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_criteria__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_reviewer__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_phases__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__display_name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_surveys__display_name
#: model:ir.model.fields,field_description:cep_ef.field_survey_send_to_wizard__display_name
msgid "Display Name"
msgstr "Nombre para mostrar"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_doc_review
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_activity_form
msgid "Document Review"
msgstr "Revisión de documentos"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_doc_review_reviewer
msgid "Document Review - Reviewer Relation"
msgstr "Revisión de documentos - Relación del revisor"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_doc_reviewer
msgid "Document Reviewers"
msgstr "Revisores de documentos"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__doc_review_ids
msgid "Document Reviews"
msgstr "Revisiones de documentos"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__document_source
msgid "Document Source"
msgstr "Fuente del documento"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__document_title
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__doc_review_id
msgid "Document Title"
msgstr "Título del documento"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__download_url
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__download_url
msgid "Document URL"
msgstr "URL del documento"

#. module: cep_ef
#: model:ir.actions.server,name:cep_ef.action_download_reviewed_file
msgid "Download Reviewed File"
msgstr "Descargar archivo revisado"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_reviewer__email
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__email
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__email
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__email
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__email
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__email
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__user_email
msgid "Email"
msgstr "Correo electrónico"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__end_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__end_date
msgid "End Date"
msgstr "Fecha de finalización"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__assembly_summary_executive
msgid "Executive Summary"
msgstr "Resumen ejecutivo"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__expert_ids
#: model:ir.model.fields.selection,name:cep_ef.selection__survey_send_to_wizard__recipient_type__experts
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_assembly_info_form
msgid "Experts"
msgstr "Expertos"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_facilitators
msgid "Facilitator"
msgstr "Facilitador"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__facilitator_ids
#: model:ir.model.fields.selection,name:cep_ef.selection__survey_send_to_wizard__recipient_type__facilitators
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_assembly_info_form
msgid "Facilitators"
msgstr "Facilitadores"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.review_pending
msgid "File"
msgstr "Archivo"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__document_filename
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__reviewed_filename
msgid "File Name"
msgstr "Nombre del archivo"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__final_document
msgid "Final Document"
msgstr "Documento final"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__final_document_filename
msgid "Final Document File Name"
msgstr "Nombre del archivo del documento final"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_final_documents_and_comments
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__final_documnet_comment_ids
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_activity_form
msgid "Final Documents and Comments"
msgstr "Documentos finales y comentarios"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__message_follower_ids
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__message_follower_ids
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__message_partner_ids
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__message_partner_ids
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (socios)"

#. module: cep_ef
#: model:ir.model.fields,help:cep_ef.field_cep_ef_activities__activity_type_icon
#: model:ir.model.fields,help:cep_ef.field_cep_ef_deliverables__activity_type_icon
#: model:ir.model.fields,help:cep_ef.field_cep_ef_responsible_members__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icono de fuente impresionante, p. tareas fa"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__has_message
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__has_message
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__has_message
msgid "Has Message"
msgstr "Tiene mensaje"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_criteria__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_reviewer__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_phases__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_surveys__id
#: model:ir.model.fields,field_description:cep_ef.field_survey_send_to_wizard__id
msgid "ID"
msgstr ""

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__activity_exception_icon
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__activity_exception_icon
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__activity_exception_icon
msgid "Icon"
msgstr "Icono"

#. module: cep_ef
#: model:ir.model.fields,help:cep_ef.field_cep_ef_activities__activity_exception_icon
#: model:ir.model.fields,help:cep_ef.field_cep_ef_deliverables__activity_exception_icon
#: model:ir.model.fields,help:cep_ef.field_cep_ef_responsible_members__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icono para indicar una actividad de excepción."

#. module: cep_ef
#: model:ir.model.fields,help:cep_ef.field_cep_ef_activities__message_needaction
#: model:ir.model.fields,help:cep_ef.field_cep_ef_deliverables__message_needaction
#: model:ir.model.fields,help:cep_ef.field_cep_ef_responsible_members__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Si está marcado, los mensajes nuevos requieren su atención."

#. module: cep_ef
#: model:ir.model.fields,help:cep_ef.field_cep_ef_activities__message_has_error
#: model:ir.model.fields,help:cep_ef.field_cep_ef_deliverables__message_has_error
#: model:ir.model.fields,help:cep_ef.field_cep_ef_responsible_members__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Si está marcado, algunos mensajes tienen un error de entrega."

#. module: cep_ef
#: model:ir.model.fields.selection,name:cep_ef.selection__cep_ef_activities__status__in_progress
#: model:ir.model.fields.selection,name:cep_ef.selection__cep_ef_doc_review__status__in_progress
#: model:ir.model.fields.selection,name:cep_ef.selection__cep_ef_surveys__status__in_progress
msgid "In Progress"
msgstr "En curso"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews__interview_format
msgid "Interview Format"
msgstr "Formato de entrevista"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews__interview_title
msgid "Interview Title"
msgstr "Título de la entrevista"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_interviews
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__interview_ids
msgid "Interviews"
msgstr "Entrevistas"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__message_is_follower
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__message_is_follower
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__message_is_follower
msgid "Is Follower"
msgstr "es seguidor"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_criteria____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_reviewer____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_phases____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members____last_update
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_surveys____last_update
#: model:ir.model.fields,field_description:cep_ef.field_survey_send_to_wizard____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_criteria__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_reviewer__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_phases__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_surveys__write_uid
#: model:ir.model.fields,field_description:cep_ef.field_survey_send_to_wizard__write_uid
msgid "Last Updated by"
msgstr "Actualizado por última vez por"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_criteria__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_reviewer__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_phases__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__write_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_surveys__write_date
#: model:ir.model.fields,field_description:cep_ef.field_survey_send_to_wizard__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__location
msgid "Location"
msgstr "Ubicación"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__message_main_attachment_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__message_main_attachment_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__message_main_attachment_id
msgid "Main Attachment"
msgstr "Adjunto principal"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__message_has_error
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__message_has_error
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__message_has_error
msgid "Message Delivery error"
msgstr "Error de entrega de mensaje"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__message_ids
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__message_ids
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__is_milestone
msgid "Milestone"
msgstr "Hito"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__my_activity_date_deadline
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__my_activity_date_deadline
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Fecha límite de mi actividad"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_reviewer__name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_final_documents_and_comments__name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__name
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__responsible_member_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__user_name
msgid "Name"
msgstr "Nombre"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__activity_date_deadline
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__activity_date_deadline
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Fecha límite para la próxima actividad"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__activity_summary
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__activity_summary
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__activity_summary
msgid "Next Activity Summary"
msgstr "Resumen de la próxima actividad"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__activity_type_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__activity_type_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__activity_type_id
msgid "Next Activity Type"
msgstr "Siguiente tipo de actividad"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.submit_review
msgid "No"
msgstr ""

#. module: cep_ef
#: model:ir.model.fields.selection,name:cep_ef.selection__cep_ef_activities__status__not_started
#: model:ir.model.fields.selection,name:cep_ef.selection__cep_ef_doc_review__status__not_started
#: model:ir.model.fields.selection,name:cep_ef.selection__cep_ef_surveys__status__not_started
msgid "Not Started"
msgstr "No iniciado"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__message_needaction_counter
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__message_needaction_counter
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__message_has_error_counter
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__message_has_error_counter
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__message_has_error_counter
msgid "Number of errors"
msgstr "Número de errores"

#. module: cep_ef
#: model:ir.model.fields,help:cep_ef.field_cep_ef_activities__message_needaction_counter
#: model:ir.model.fields,help:cep_ef.field_cep_ef_deliverables__message_needaction_counter
#: model:ir.model.fields,help:cep_ef.field_cep_ef_responsible_members__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Número de mensajes que requieren acción"

#. module: cep_ef
#: model:ir.model.fields,help:cep_ef.field_cep_ef_activities__message_has_error_counter
#: model:ir.model.fields,help:cep_ef.field_cep_ef_deliverables__message_has_error_counter
#: model:ir.model.fields,help:cep_ef.field_cep_ef_responsible_members__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de entrega"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.submit_review
msgid ""
"Once submitted, it cannot\n"
"                                be edited or changed!"
msgstr ""

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__organization_ids
#: model:ir.model.fields.selection,name:cep_ef.selection__survey_send_to_wizard__recipient_type__organizations
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_assembly_info_form
msgid "Organizations"
msgstr "Organizaciones"

#. module: cep_ef
#: model:ir.model.fields.selection,name:cep_ef.selection__survey_send_to_wizard__recipient_type__participants
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_assembly_info_form
msgid "Other Participants"
msgstr "Otros participantes"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_participants
msgid "Participant"
msgstr "Partícipe"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__participant_ids
msgid "Participants"
msgstr "Participantes"

#. module: cep_ef
#: model:ir.model.fields.selection,name:cep_ef.selection__cep_ef_doc_review_reviewer__status__pending
msgid "Pending"
msgstr "Pendiente"

#. module: cep_ef
#: model:ir.actions.act_window,name:cep_ef.cep_ef_phase_action
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__phase_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_criteria__phase_id
#: model:ir.ui.menu,name:cep_ef.cep_ef_phase
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_phase_form
msgid "Phase"
msgstr "Fase"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_phases__description
msgid "Phase Description"
msgstr "Descripción de la fase"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_phases__phase_name
msgid "Phase Name"
msgstr "Nombre de fase"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__assembly_summary_public
msgid "Public Summary"
msgstr "Resumen público"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews__questionnaire_link
msgid "Questionnaire Link"
msgstr "Enlace al cuestionario"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_survey_send_to_wizard__recipient_type
msgid "Recipient Type"
msgstr "Tipo de destinatario"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_interviews__recording_format
msgid "Recording Format"
msgstr "Formato de grabación"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_assembly_info_form
msgid "Relevant Authorities:"
msgstr "Autoridades Relevantes:"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_responsible_members
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__responsible_member_ids
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_activity_form
msgid "Responsible Members"
msgstr "Miembros responsables"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__activity_user_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_deliverables__activity_user_id
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__activity_user_id
msgid "Responsible User"
msgstr "Usuario responsable"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__review_deadline
msgid "Review Deadline"
msgstr "Fecha límite de revisión"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.review_pending
msgid "Review Pending Documents"
msgstr "Revisar documentos pendientes"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.submit_review
msgid ""
"Review submit\n"
"                                confirmation"
msgstr ""

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__reviewed_file
msgid "Reviewed File"
msgstr "Archivo revisado"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__doc_reviewer_id
msgid "Reviewer"
msgstr "Crítico"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__reviewer_comments
msgid "Reviewer Comments"
msgstr "Comentarios del revisor"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__doc_reviewers_ids
msgid "Reviewers"
msgstr "Revisores"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__role
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__role
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__role
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__role
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__role
msgid "Role"
msgstr "Role"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.view_survey_send_to_wizard_form
msgid "Send"
msgstr "Enviar"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_survey_send_to_wizard
msgid "Send Survey To Participants, Experts, Facilitators, Organizations"
msgstr "Enviar encuesta a participantes, expertos, facilitadores y organizaciones"

#. module: cep_ef
#: model:ir.actions.act_window,name:cep_ef.action_survey_send_to_wizard
msgid "Send Survey To Recipients"
msgstr "Enviar encuesta a los destinatarios"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_activity_form
msgid "Send To"
msgstr "Enviar a"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__start_date
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_assembly_info__start_date
msgid "Start Date"
msgstr "Fecha de inicio"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__status
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__status
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review_reviewer__status
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_surveys__status
msgid "Status"
msgstr "Estado"

#. module: cep_ef
#: model:ir.model.fields,help:cep_ef.field_cep_ef_activities__activity_state
#: model:ir.model.fields,help:cep_ef.field_cep_ef_deliverables__activity_state
#: model:ir.model.fields,help:cep_ef.field_cep_ef_responsible_members__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.submit_review
msgid ""
"Submit\n"
"                                                    Review"
msgstr ""

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.submit_review
msgid "Submit Review"
msgstr "Enviar reseña"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.review_pending
msgid "Submit your review"
msgstr "Envía tu reseña"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_activity_form
msgid "Submited reviews"
msgstr "Reseñas enviadas"

#. module: cep_ef
#: model:ir.model.fields.selection,name:cep_ef.selection__cep_ef_doc_review_reviewer__status__submitted
msgid "Submitted"
msgstr "Enviado"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_submitted_reviews_form
msgid "Submitted Reviews"
msgstr "Reseñas enviadas"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_survey_send_to_wizard__survey_id
msgid "Survey"
msgstr "Encuesta"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_surveys__survey_link
msgid "Survey Link"
msgstr "Enlace de encuesta"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_surveys__survey_name
msgid "Survey Name"
msgstr "Nombre de la encuesta"

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_surveys
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_activities__survey_ids
msgid "Surveys"
msgstr "Encuestas"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.view_cep_ef_activity_form
msgid "Surveys/Interviews"
msgstr "Encuestas/Entrevistas"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_experts__task
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_facilitators__task
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_participants__task
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_responsible_members__task
msgid "Task"
msgstr "Tarea"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.review_pending
msgid "Title"
msgstr "Título"

#. module: cep_ef
#: model:ir.model.fields,help:cep_ef.field_cep_ef_activities__activity_exception_decoration
#: model:ir.model.fields,help:cep_ef.field_cep_ef_deliverables__activity_exception_decoration
#: model:ir.model.fields,help:cep_ef.field_cep_ef_responsible_members__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de actividad de excepción registrada."

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_doc_review__document_file
msgid "Upload Document"
msgstr "Cargar documento"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.submit_review
msgid "Upload Reviewed File"
msgstr "Cargar archivo revisado"

#. module: cep_ef
#: model:ir.model.fields,field_description:cep_ef.field_cep_ef_organizations__website
msgid "Website"
msgstr "Sitio web"

#. module: cep_ef
#: model_terms:ir.ui.view,arch_db:cep_ef.submit_review
msgid "Yes"
msgstr ""

#. module: cep_ef
#: model:ir.model,name:cep_ef.model_cep_ef_experts
msgid "experts"
msgstr "expertos"
