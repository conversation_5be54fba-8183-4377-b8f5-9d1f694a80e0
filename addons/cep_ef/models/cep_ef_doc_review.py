from odoo import models, fields, api
from datetime import datetime, timedelta


class DocumentReview(models.Model):
    _name = 'cep.ef.doc_review'
    _description = 'Document Review'
    _rec_name = 'document_title'

    document_title = fields.Char(string='Document Title', required=True)
    document_source = fields.Char(string='Document Source', required=True)
    document_file = fields.Binary(
        string='Upload Document', required=True, attachment=True)
    document_filename = fields.Char(string='File Name', )
    status = fields.Selection([
        ('not_started', 'Not Started'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
    ], string='Status', default='not_started', required=True)
    review_deadline = fields.Date(string='Review Deadline', required=True,
                                  default=lambda self: datetime.now() + timedelta(days=7))

    activity_id = fields.Many2one(
        'cep.ef.activities', string='Activity', required=True)
    # doc_reviewers_ids = fields.Many2many(
    #     'cep.ef.doc.reviewer',
    #     'cep_ef_doc_review_reviewer',
    #     'doc_review_id',
    #     'doc_reviewer_id',
    #     string='Reviewers',
    #     domain="[('assembly_info_id', '=', assembly_info_id)]"
    # )
    #     domain="[('assembly_info_id', '=', assembly_info_id)]"
    doc_reviewers_ids = fields.Many2many(
        'cep.ef.doc.reviewer', string='Reviewers', domain="[('assembly_info_id', '=', assembly_info_id)]")
    # doc_reviewer_feedback_ids = fields.Many2One('cep.ef.reviewer_feedback', string='Reviewers Feedback')
    assembly_info_id = fields.Many2one('cep.ef.assembly.info', string='Assembly Info',
                                       compute='_compute_assembly_info_id', store=True, ondelete='cascade')
    download_url = fields.Char(
        string='Document URL', compute='_compute_document_url')

    # Compute the Assembly Info ID from the Activity
    @api.depends('activity_id')
    def _compute_assembly_info_id(self):
        for record in self:
            record.assembly_info_id = record.activity_id.assembly_info_id.id if record.activity_id else False

    # Compute the Document URL for download button in website portal view
    @api.depends('document_file', 'document_title')
    def _compute_document_url(self):
        for record in self:
            if record.document_file:
                # record.download_url = f'/web/content/{record.id}/document_file/{record.document_title}'
                record.download_url = '/web/content/%s/%s/document_file/%s' % (
                    record._name, record.id, record.document_title) + '?download=true'
            else:
                record.download_url = False

    # Review button action to open the Document Reviewers tree view

    def action_view_reviewers(self):
        self.ensure_one()

        # Fetch the related doc_review_reviewer records for the current doc_review
        review_records = self.env['cep.ef.doc_review_reviewer'].search([
            ('doc_review_id', '=', self.id),
            ('doc_reviewer_id', 'in', self.doc_reviewers_ids.ids)
        ])

        # Extract the ids of these review_records
        review_record_ids = review_records.ids

        # Construct the domain dynamically based on these review_record_ids
        domain = [('id', 'in', review_record_ids)]

        return {
            'type': 'ir.actions.act_window',
            'name': 'Submitted Reviews',
            'view_mode': 'tree,form',
            'res_model': 'cep.ef.doc_review_reviewer',
            'domain': domain,
            'context': dict(self._context, create=False)
        }

    # Send email to document reviewers
    def send_email_to_reviewers(self):
        for reviewer in self.doc_reviewers_ids:
            subject = f'Document Review for "{self.document_title}"'
            body = f'Dear {reviewer.name},<br><br>' \
                f'You have been selected as a reviewer for the document "{self.document_title}". Please login/signup in https://citizentone.com to review the document before "{self.review_deadline}".<br><br>' \
                f'Best regards,<br>The Citizentone Team'

            # Send the email
            self.env['mail.mail'].sudo().create({
                'subject': subject,
                'body_html': body,
                'email_to': reviewer.email,
            }).send()

    # Document Review CRUD operations logging in the Activity chatter
    def write(self, vals):
        for record in self:
            # changes = []
            # for field, new_value in vals.items():
            #     if field in record._fields:
            #         old_value = record[field]
            #         field_name = record._fields[field].string
            #         # Convert relational fields to display names for better readability
            #         if record._fields[field].type in ['many2one', 'one2many', 'many2many']:
            #             old_value = old_value.name if old_value else 'False'
            #             new_value = self.env[record._fields[field].comodel_name].browse(new_value).name if new_value else 'False'
            #         changes.append(f"{field_name}: {old_value} <i class=\"fa fa-long-arrow-right\" aria-hidden=\"true\"></i> <span class=\"o_TrackingValue_newValue me-1 fw-bold text-info\">{new_value}</span>")

            # if changes:
            #     change_message = "<br/>".join(changes)
            #     record.activity_id.message_post(
            #         body=f"Document Review '{record.document_title}' has been updated.<br/>{change_message}"
            #     )

            record.activity_id.message_post(
                body=f"Document Review '{record.document_title}' has been updated.<br/>"
            )

        return super(DocumentReview, self).write(vals)

    def unlink(self):
        #! (NOT WORKING) Delete attachments for each related review_reviewer_recs
        Attachment = self.env['ir.attachment']
        for record in self:
            # Find related cep.ef.doc_review_reviewer records
            review_reviewer_recs = self.env['cep.ef.doc_review_reviewer'].search([
                ('doc_review_id', '=', record.id)
            ])
            # Delete attachments for each related review_reviewer_recs
            for review_reviewer_rec in review_reviewer_recs:
                attachments = Attachment.search([
                    ('res_model', '=', 'cep.ef.doc_review_reviewer'),
                    ('res_id', '=', review_reviewer_rec.id),
                    ('name', '=', 'reviewed_file')
                ])
                print('*'*50)
                print(attachments)
                attachments.unlink()
        # Post delete message in the Activity chatter
        for record in self:
            record.activity_id.message_post(
                body=f"Document Review '{record.document_title}' has been deleted."
            )
        return super(DocumentReview, self).unlink()

    @api.model
    def create(self, vals):
        record = super(DocumentReview, self).create(vals)
        record.send_email_to_reviewers()
        record.activity_id.message_post(
            body=f"Document Review '{record.document_title}' has been created."
        )
        return record
