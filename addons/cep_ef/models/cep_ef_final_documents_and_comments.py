from odoo import models, fields, api
from odoo.exceptions import ValidationError
from odoo.tools import single_email_re


class FinalDocumentsAndComments(models.Model):
    _name = 'cep.ef.final_documents_and_comments'
    _description = 'Final Documents and Comments'

    name = fields.Char(string='Name', required=True)
    email = fields.Char(string='Email', required=True)
    final_document = fields.Binary(string='Final Document', attatchment=True)
    final_document_filename = fields.Char(string='Final Document File Name', )
    comments = fields.Text(string='Comments', required=True)

    activity_id = fields.Many2one(
        'cep.ef.activities', string='Activity', required=True, ondelete='cascade')

    @api.onchange('email')
    def _onchange_email(self):
        if self.email and not single_email_re.match(self.email):
            raise ValidationError('Your entered email is invalid!')
