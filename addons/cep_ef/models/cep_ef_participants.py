from odoo import models, fields, api
from odoo.exceptions import ValidationError
from odoo.tools import single_email_re


class Participant(models.Model):
    _name = 'cep.ef.participants'
    _description = 'Participant'
    _rec_name = 'name'

    name = fields.Char(string='Name', required=True)
    email = fields.Char(string='Email', required=True)
    role = fields.Char(string='Role')
    background = fields.Char(string='Background')
    task = fields.Char(string='Task')

    assembly_info_id = fields.Many2one(
        'cep.ef.assembly.info', string='Assembly Info', required=True, ondelete='cascade')

    @api.constrains('email')
    def _check_email_format(self):
        for record in self:
            if record.email and not single_email_re.match(record.email):
                raise ValidationError(
                    "Please provide a valid email address in Paticipants section.")

    # Overriding the CRUD operations to create a copy of Participant records in the Document Reviewers

    @api.model
    def create(self, vals):
        record = super(Participant, self).create(vals)
        # VALIDATION: check if the reviewer already exists in the cep_ef_doc_reviewer table under same assembly info
        reviewer_exist = self.env['cep.ef.doc.reviewer'].search(
            [('email', '=', record.email), ('assembly_info_id', '=', record.assembly_info_id.id)], limit=1)

        if not reviewer_exist:
            self.env['cep.ef.doc.reviewer'].create({
                'name': record.name,
                'email': record.email,
                'assembly_info_id': record.assembly_info_id.id,
            })

        return record

    def write(self, vals):
        result = super(Participant, self).write(vals)
        for record in self:
            reviewer = self.env['cep.ef.doc.reviewer'].search(
                [('name', '=', record.name), ('assembly_info_id', '=', record.assembly_info_id.id)], limit=1)
            if reviewer:
                reviewer.write({
                    'name': record.name,
                    'email': record.email,
                    'assembly_info_id': record.assembly_info_id.id,
                })
            else:
                self.env['cep.ef.doc.reviewer'].create({
                    'name': record.name,
                    'email': record.email,
                    'assembly_info_id': record.assembly_info_id.id,
                })
        return result

    def unlink(self):
        for record in self:
            reviewer = self.env['cep.ef.doc.reviewer'].search([
                ('name', '=', record.name),
                ('email', '=', record.email),
                ('assembly_info_id', '=', record.assembly_info_id.id)
            ], limit=1)
            if reviewer:
                reviewer.unlink()
        return super(Participant, self).unlink()

    @api.onchange('email')
    def _onchange_email(self):
        if self.email and not single_email_re.match(self.email):
            raise ValidationError('Your entered email is invalid!')
