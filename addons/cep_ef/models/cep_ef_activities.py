from odoo import models, fields, api
from datetime import datetime


class Activity(models.Model):
    _name = 'cep.ef.activities'
    _description = 'Activities'
    _rec_name = 'activity_name'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    ALLOWED_CURRENCY_NAME = ['GBP', 'USD']

    activity_name = fields.Char(
        string='Activity Name', required=True, tracking=True, translate=True)
    currency_id = fields.Many2one(
        'res.currency',
        String='Currency',
        default=lambda self: self.env['res.currency'].search(
            [('name', '=', 'GBP')], limit=1),
        domain=[('name', 'in', ALLOWED_CURRENCY_NAME)],
        required=True,
        tracking=True
    )
    budget = fields.Monetary(
        string='Budget', currency_field='currency_id', required=True, tracking=True
    )
    description = fields.Text(string='Description', translate=True)
    start_date = fields.Date(string='Start Date', default=lambda self: (
        datetime.now()).strftime('%Y-%m-%d'), required=True, tracking=True)
    end_date = fields.Date(string='End Date', default=lambda self: (
        datetime.now()).strftime('%Y-%m-%d'), required=True, tracking=True)
    status = fields.Selection([
        ('not_started', 'Not Started'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
    ], string='Status', default='not_started', required=True, tracking=True)
    is_milestone = fields.Boolean('Milestone')

    assembly_info_id = fields.Many2one(
        'cep.ef.assembly.info', string='Assembly Info', ondelete='cascade')
    deliverable_ids = fields.One2many(
        'cep.ef.deliverables', 'activity_id', string='Deliverables')
    phase_id = fields.Many2one(
        'cep.ef.phases', string='Phase', required=True, tracking=True)
    criteria_id = fields.Many2one(
        'cep.ef.criteria', string='Criteria', required=True, tracking=True)
    survey_ids = fields.One2many(
        'cep.ef.surveys', 'activity_id', string='Surveys')
    interview_ids = fields.One2many(
        'cep.ef.interviews', 'activity_id', string='Interviews')
    responsible_member_ids = fields.One2many(
        'cep.ef.responsible_members', 'activity_id', string='Responsible Members')
    doc_review_ids = fields.One2many(
        'cep.ef.doc_review', 'activity_id', string='Document Reviews')
    final_documnet_comment_ids = fields.One2many(
        'cep.ef.final_documents_and_comments', 'activity_id', string='Final Documents and Comments')

    @api.onchange('phase_id')
    def onchange_phase_id(self):
        """
        Update the domain of criteria_id based on the selected phase.
        """
        if self.phase_id:
            return {'domain': {'criteria_id': [('phase_id', '=', self.phase_id.id)]}}
        else:
            return {'domain': {'criteria_id': []}}

    def action_open_assembly_info(self):
        # Logic to open assembly info
        return {
            'name': 'Assembly Info',
            'view_mode': 'form',
            'res_model': 'cep.ef.assembly.info',
            'type': 'ir.actions.act_window',
            'res_id': self.assembly_info_id.id,
            'target': 'current',
        }

    def action_open_activity_list(self):
        # Logic to open activity list
        return {
            'name': 'Activity List',
            'view_mode': 'tree,form',
            'res_model': 'cep.ef.activities',
            'type': 'ir.actions.act_window',
            'domain': [('assembly_info_id', '=', self.assembly_info_id.id)],
            'context': {
                'default_assembly_info_id': self.assembly_info_id.id,
            },
            'target': 'current',
        }
