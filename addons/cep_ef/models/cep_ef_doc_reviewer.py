from odoo import models, fields, api
from odoo.exceptions import ValidationError
from odoo.tools import single_email_re


class DocumentReviewer(models.Model):
    _name = 'cep.ef.doc.reviewer'
    _description = 'Document Reviewers'
    _rec_name = 'name'

    name = fields.Char(string='Name', required=True)
    email = fields.Char(string='Email', required=True)
    # reviewed_file = fields.Binary(string='Upload Review')
    # reviewer_comments = fields.Text(string='Reviewer Comments')
    # status = fields.Selection([
    #     ('pending', 'Pending'),
    #     ('submitted', 'Submitted'),
    # ], string='Status', default='pending', required=True)
    # download_url = fields.Char(string='Document URL', compute='_compute_document_url')

    # assembly id to filter the reviewers based on the assembly
    assembly_info_id = fields.Many2one(
        'cep.ef.assembly.info', string='Assembly Info', required=True, ondelete='cascade')

    # This method is used to display the name and email of the expert in the many2one field
    @api.model
    def name_get(self):
        result = []
        for reviewer in self:
            name = f"{reviewer.name} ({reviewer.email})"
            result.append((reviewer.id, name))
        return result

    # @api.depends('reviewed_file')
    # def _compute_document_url(self):
    #     for record in self:
    #         if record.reviewed_file:
    #             record.download_url = f'/web/content/{record._name}/{record.id}/reviewed_file?download=true'
    #         else:
    #             record.download_url = False
    @api.onchange('email')
    def _onchange_email(self):
        if self.email and not single_email_re.match(self.email):
            raise ValidationError('Your entered email is invalid!')
