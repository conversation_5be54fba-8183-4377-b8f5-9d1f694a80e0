from odoo import models, fields, api
from odoo.exceptions import ValidationError
from odoo.tools import single_email_re


class Organizations(models.Model):
    _name = 'cep.ef.organizations'
    _description = 'CEP EF Organizations'
    _rec_name = 'name'

    name = fields.Char(string='Name', required=True)
    email = fields.Char(string='Email', required=True)
    address = fields.Char(string='Address', required=True)
    website = fields.Char(string='Website', required=True)
    role = fields.Char(string='Role')

    assembly_info_id = fields.Many2one(
        'cep.ef.assembly.info', string='Assembly Info', required=True, ondelete='cascade')

    @api.constrains('email')
    def _check_email_format(self):
        for record in self:
            if record.email and not single_email_re.match(record.email):
                raise ValidationError(
                    "Please provide a valid email address in Organization section.")

    @api.onchange('email')
    def _onchange_email(self):
        if self.email and not single_email_re.match(self.email):
            raise ValidationError('Your entered email is invalid!')
