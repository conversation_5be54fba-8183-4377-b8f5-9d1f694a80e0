from odoo import models, fields, api


class Responsible<PERSON>ember(models.Model):
    _name = 'cep.ef.responsible_members'
    _description = 'Responsible Members'
    _rec_name = 'responsible_member_id'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    # name = fields.Char(string='Name', required=True)
    responsible_member_id = fields.Many2one(
        'res.users', string='Name', index=True, tracking=True, required=True)
    role = fields.Char(string='Role', required=True)
    # email = fields.Char(string='Email', required=True)
    background = fields.Char(string='Background', required=True)
    task = fields.Char(string='Task', required=True)
    # phone = fields.Char(string='Phone')

    activity_id = fields.Many2one(
        'cep.ef.activities', string='Activity', required=True, ondelete='cascade')

    @api.depends('responsible_member_id')
    def _compute_user_details(self):
        for record in self:
            record.user_email = record.responsible_member_id.email
            record.user_name = record.responsible_member_id.name

    user_email = fields.Char(string='Email', compute='_compute_user_details')
    user_name = fields.Char(string='Name', compute='_compute_user_details')

    # Responsible Person CRUD operations logging in the Activity chatter

    def write(self, vals):
        for record in self:
            changes = []
            for field, new_value in vals.items():
                if field in record._fields:
                    old_value = record[field]
                    field_name = record._fields[field].string
                    # Convert relational fields to display names for better readability
                    if record._fields[field].type in ['many2one', 'one2many', 'many2many']:
                        old_value = old_value.name if old_value else 'False'
                        new_value = self.env[record._fields[field].comodel_name].browse(
                            new_value).name if new_value else 'False'
                    changes.append(
                        f"{field_name}: {old_value} <i class=\"fa fa-long-arrow-right\" aria-hidden=\"true\"></i> <span class=\"o_TrackingValue_newValue me-1 fw-bold text-info\">{new_value}</span>")

            if changes:
                change_message = "<br/>".join(changes)
                record.activity_id.message_post(
                    body=f"Responsible member '{record.responsible_member_id.name}' has been updated.<br/>{change_message}"
                )

        return super(ResponsibleMember, self).write(vals)

    def unlink(self):
        for record in self:
            record.activity_id.message_post(
                body=f"Responsible member '{record.responsible_member_id.name}' has been deleted."
            )
        return super(ResponsibleMember, self).unlink()

    @api.model
    def create(self, vals):
        record = super(ResponsibleMember, self).create(vals)
        record.activity_id.message_post(
            body=f"Responsible member '{record.responsible_member_id.name}' has been created."
        )

        # print recipient name/email
        print('*'*50)
        print(record.user_email)
        print(record.user_name)
        # print activity name
        print(record.activity_id.activity_name)
        print(record.activity_id.assembly_info_id.assembly_title)

        subject = f'Responsible Person for Activity "{record.activity_id.activity_name}"'
        body = f'Dear {record.user_name},<br><br>' \
            f'You have been selected as a responsible member for Activity - "{record.activity_id.activity_name}" of Climate Assembly - "{record.activity_id.assembly_info_id.assembly_title}".<br><br>' \
            f'Best regards,<br><a href="https://citizentone.com">The Citizentone Team</a>'

        # Send the email
        self.env['mail.mail'].sudo().create({
            'subject': subject,
            'body_html': body,
            'email_to': record.user_email,
        }).send()
        return record
