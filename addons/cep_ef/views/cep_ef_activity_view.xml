<?xml version="1.0"?>
<odoo>

    <record id="action_open_target_model_tree_view" model="ir.actions.act_window">
        <field name="name">Survey redirect</field>
        <field name="res_model">survey.survey</field>
        <field name="view_mode">tree,form</field>
        <field name="target">main</field>
    </record>
    <!-- Search (Filter) View -->
    <record id="view_cep_ef_activities_search" model="ir.ui.view">
        <field name="name">cep.ef.activities.search</field>
        <field name="model">cep.ef.activities</field>
        <field name="arch" type="xml">
            <search string="CEP EF Activity">
                <field name="activity_name" />
                <field name="description" />
            </search>
        </field>
    </record>

    <!-- List (Tree) View -->
    <record id="view_cep_ef_activity_tree" model="ir.ui.view">
        <field name="name">cep.ef.activities.tree</field>
        <field name="model">cep.ef.activities</field>
        <field name="arch" type="xml">
            <tree>
                <field name="activity_name" />
                <field name="phase_id" />
                <field name="criteria_id" />
                <field name="start_date" />
                <field name="end_date" />
                <field name="status" widget="badge" decoration-danger="status == 'not_started'"
                    decoration-warning="status == 'in_progress'"
                    decoration-success="status == 'completed'" />
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_cep_ef_activity_form" model="ir.ui.view">
        <field name="name">cep.ef.activities.form</field>
        <field name="model">cep.ef.activities</field>
        <field name="arch" type="xml">
            <form string="Activity">
                <header>
                    <!-- Add a button box on top of the form view -->
                    <!-- <button string="Phases" class="oe_highlight" icon="fa-bolt" type="object"
                    name="action_open_project_phase_form_view"/> -->
                    <!-- <button string="Assembly Info" class="oe_highlight" icon="fa-arrow-left"
                    type="object"
                    name="action_open_assembly_info"/>
                    <button string="Activity List" class="oe_highlight" icon="fa-list" type="object"
                    name="action_open_activity_list"/> -->
                    <field name="status" widget="statusbar"
                        options="{'clickable': '1', 'fold_field': 'fold'}" />
                </header>
                <sheet>
                    <h3>Activity</h3>
                    <hr />
                    <!-- <field name="cover_photo" widget="image" class="oe_avatar"/> -->
                    <group>
                        <field name="activity_name" />
                        <field name="phase_id" options="{'no_create': False, 'no_open': False}"
                            on_change="onchange_phase_id(phase_id)" />
                        <field name="criteria_id" options="{'no_create': True}" />
                        <field name="currency_id" widget="selection" />
                        <field name="budget" widget="monetary" />
                        <field name="description" />
                        <!-- <field name="tag_ids" widget="many2many_tags"/> -->
                        <field name="start_date" />
                        <field name="end_date" />
                    </group>
                    <notebook>
                        <!-- Responsible Members -->
                        <page name="responsible_members" string="Responsible Members">
                            <field name="responsible_member_ids" nolabel="1">
                                <tree>
                                    <!-- <field name="name"/> -->
                                    <field name="responsible_member_id" />
                                    <field name="role" />
                                    <field name="user_email" />
                                    <field name="task" />
                                </tree>
                                <form>
                                    <group>
                                        <!-- <field name="name"/> -->
                                        <field name="responsible_member_id" />
                                        <field name="role" />
                                        <!-- <field name="email" widget="email"/> -->
                                        <field name="background" />
                                        <field name="task" />
                                        <!-- <field name="phone"/> -->
                                    </group>
                                </form>
                            </field>
                        </page>
                        <!-- Deliverables -->
                        <page name="deliverables" string="Deliverables">
                            <field name="deliverable_ids" nolabel="1">
                                <tree>
                                    <field name="deliverable_name" />
                                    <field name="deadline" />
                                </tree>
                                <form>
                                    <group>
                                        <field name="deliverable_name" />
                                        <field name="deadline" />
                                    </group>
                                </form>
                            </field>
                        </page>
                        <!-- Surveys -->
                        <page name="surveys" string="Surveys/Interviews" attrs-data-key="surveys">
                            <button name="%(cep_ef.action_open_target_model_tree_view)d"
                                type="action"
                                string="Go to Surveys " class="btn-primary btn oe_highlight mb-2" />
                            <field name="survey_ids" nolabel="1">
                                <tree>
                                    <field name="survey_name" />
                                    <field name="status" />
                                    <button string="Send To" class="oe_highlight" type="object"
                                        name="action_send_to" />

                                </tree>

                                <form>
                                    <group>
                                        <field name="survey_name" />
                                        <field name="survey_link" />
                                        <field name="status" widget="selection" />
                                    </group>


                                </form>
                            </field>

                        </page>
                        <!-- Interviews -->
                        <!-- <page name="interviews" string="Interviews">
                            <field name="interview_ids" nolabel="1">
                                <tree>
                                    <field name="interview_title"/>
                                    <field name="questionnaire_link"/>
                                    <field name="interview_format"/>
                                    <field name="recording_format"/>
                                </tree>
                                <form>
                                    <group>
                                        <field name="interview_title"/>
                                        <field name="questionnaire_link"/>
                                        <field name="interview_format"/>
                                        <field name="recording_format"/>
                                    </group>
                                </form>
                            </field>
                        </page> -->
                        <!-- Document Review -->
                        <page name="document_review" string="Document Review">
                            <field name="doc_review_ids" nolabel="1">
                                <tree>
                                    <field name="document_title" />
                                    <field name="document_source" />
                                    <field name="review_deadline" />
                                    <field name="status" />
                                    <button name="action_view_reviewers" type="object"
                                        string="Submited reviews"
                                        class="oe_highlight" />
                                </tree>
                                <form>
                                    <group>
                                        <field name="document_title" />
                                        <field name="document_source" />
                                        <field name="document_filename" invisible="1" />
                                        <field name="document_file" widget="binary"
                                            filename="document_filename" />
                                        <!-- File name display -->
                                        <field name="review_deadline" />
                                        <field name="status" widget="selection" />
                                        <field name="assembly_info_id" invisible="1" />
                                        <field name="doc_reviewers_ids" widget="many2many_tags"
                                            options="{'no_create': true}" />
                                    </group>
                                </form>
                            </field>
                        </page>
                        <!-- Final Documents and Comments -->
                        <page name="final_documnet_comment" string="Final Documents and Comments">
                            <field name="final_documnet_comment_ids" nolabel="1">
                                <tree>
                                    <field name="name" />
                                    <field name="email" />
                                    <field name="comments" />
                                    <field name="final_document_filename" />
                                </tree>
                                <form>
                                    <group>
                                        <field name="name" />
                                        <field name="email" />
                                        <field name="comments" />
                                        <field name="final_document"
                                            filename="final_document_filename" />
                                        <field name="final_document_filename" invisible="1" />
                                    </group>
                                </form>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread" />
                    <field name="message_follower_ids" widget="mail_followers" />
                </div>
            </form>
        </field>
    </record>

    <!-- Calender View -->
    <record id="view_cep_ef_activity_calendar" model="ir.ui.view">
        <field name="name">cep.ef.activities.calendar</field>
        <field name="model">cep.ef.activities</field>
        <field name="arch" type="xml">
            <calendar string="CEP EF Activity" date_start="start_date">
                <field name="activity_name" />
                <!-- <field name="phase_id"/>
                <field name="criteria_id"/>
                <field name="start_date"/>
                <field name="end_date"/> -->
            </calendar>
        </field>
    </record>

    <record id="cep_ef_activities_action" model="ir.actions.act_window">
        <field name="name">CEP EF Activities</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">cep.ef.activities</field>
        <field name="view_mode">tree,form,calendar</field>
    </record>

    <menuitem id="cep_ef_activity" name="Activity" parent="cep_ef_root"
        action="cep_ef_activities_action" />
</odoo>