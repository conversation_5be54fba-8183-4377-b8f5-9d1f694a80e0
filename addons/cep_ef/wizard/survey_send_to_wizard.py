# your_module/wizard/survey_send_to_wizard.py
from odoo import api, fields, models
from odoo.exceptions import UserError


class SurveySendToWizard(models.TransientModel):
    _name = 'survey.send.to.wizard'
    _description = 'Send Survey To Participants, Experts, Facilitators, Organizations'

    survey_id = fields.Many2one(
        'cep.ef.surveys', string='Survey', required=True)
    recipient_type = fields.Selection([
        ('experts', 'Experts'),
        ('facilitators', 'Facilitators'),
        ('organizations', 'Organizations'),
        ('participants', 'Other Participants'),
        ('all', 'All')
    ], string='Recipient Type', required=True, default='experts')

    def action_send(self):
        if not self.recipient_type:
            raise UserError("Please select a recipient type.")

        recipients = []
        if self.recipient_type == 'participants':
            recipients = self.env['cep.ef.participants'].search([
                ('assembly_info_id', '=',
                 self.survey_id.activity_id.assembly_info_id.id)
            ])
        elif self.recipient_type == 'experts':
            recipients = self.env['cep.ef.experts'].search([
                ('assembly_info_id', '=',
                 self.survey_id.activity_id.assembly_info_id.id)
            ])
        elif self.recipient_type == 'facilitators':
            recipients = self.env['cep.ef.facilitators'].search([
                ('assembly_info_id', '=',
                 self.survey_id.activity_id.assembly_info_id.id)
            ])
        elif self.recipient_type == 'organizations':
            recipients = self.env['cep.ef.organizations'].search([
                ('assembly_info_id', '=',
                 self.survey_id.activity_id.assembly_info_id.id)
            ])
        elif self.recipient_type == 'all':
            participants = self.env['cep.ef.participants'].search([
                ('assembly_info_id', '=',
                 self.survey_id.activity_id.assembly_info_id.id)
            ])
            experts = self.env['cep.ef.experts'].search([
                ('assembly_info_id', '=',
                 self.survey_id.activity_id.assembly_info_id.id)
            ])
            facilitators = self.env['cep.ef.facilitators'].search([
                ('assembly_info_id', '=',
                 self.survey_id.activity_id.assembly_info_id.id)
            ])
            organizations = self.env['cep.ef.organizations'].search([
                ('assembly_info_id', '=',
                 self.survey_id.activity_id.assembly_info_id.id)
            ])
            recipients = participants + experts + facilitators + organizations

        if not recipients:
            raise UserError("No recipients found for the selected type.")

        # loop through recipients and send email
        for recipient in recipients:
            # print recipient email
            print(recipient.email)
            # print survey link
            print(self.survey_id.survey_link)

            subject = f'Complete survey "{self.survey_id.survey_name}" for Activity "{self.survey_id.activity_id.activity_name}"'
            body = f'Dear {recipient.name},<br><br>' \
                f'Please complete this survey from the link - {self.survey_id.survey_link}.<br><br>' \
                f'Best regards,<br>The Citizentone Team'

            # Send the email
            self.env['mail.mail'].sudo().create({
                'subject': subject,
                'body_html': body,
                'email_to': recipient.email,
            }).send()

        # template_id = self.env.ref('cep_ef.email_template_survey').id
        # template = self.env['mail.template'].browse(template_id)
        # for recipient in recipients:
        #     template.send_mail(self.survey_id.id, force_send=True, email_values={'email_to': recipient.email})

        # Return a compound action
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Success',
                'message': 'Survey sent successfully!',
                'type': 'success',  # types: success, warning, danger, info
                'sticky': False,  # True/False will display for few seconds if False
                # 'next': {
                #     'type': 'ir.actions.act_window',
                #     'res_model': self._name,
                #     'view_mode': 'form',
                #     'view_type': 'form',
                #     'res_id': self.id,
                #     'target': 'new',
                # }
            }
        }
