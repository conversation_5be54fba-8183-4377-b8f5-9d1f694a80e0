from odoo import models, fields, api
from datetime import datetime, timedelta
import logging
import json
import threading
_logger = logging.getLogger(__name__)

class Projects(models.Model):
    _name = 'cep.ag.project'
    _description = 'Project'
    _rec_name = 'title'

    title = fields.Char(string='Title', required=True)
    description = fields.Text(string='Description')
    keywords = fields.Char(string='Keywords', )
    owner_id = fields.Many2one('res.users', string='Owner', index=True, tracking=True, default=lambda self: self.env.user)
    target_country = fields.Char(string='Country', )
    start_year = fields.Char(string='Start Year', )
    end_year = fields.Char(string='End Year', )
    favorite = fields.Boolean(string='Favorite', )
    category = fields.Selection([
        ('climate_assembly', 'Climate Assembly'),
        ('climate_dialogue', 'Climate Dialogue'),
        ('just_transition_dialogue', 'Just Transition Dialogue')
    ], string='Category', required=True, default='climate_assembly')
    agenda_source_ids = fields.One2many('cep.ag.agenda_source', 'project_id', string='Sources')
    result_ids = fields.One2many('cep.ag.result', 'project_id', string='Results')
    error_message =  fields.Char(string='Error Message', )
    criteria_weights = fields.Text(string='Criteria Weights', default="{}" )
    status = fields.Selection([
        ('draft', 'Draft'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ], string='Status', default='draft')
    analyzer_status = fields.Selection([
        ('idle', 'Idle'),
        ('running', 'Running'),
        ('completed', 'Completed')
    ], string='Analyzer Status', default='idle')
    
    def save_failed_message(self, body): 
        project_id = body.get('project_id')
        status = body.get('status')
        message = body.get('message')
        
        project = self.env['cep.ag.project'].browse(int(project_id)).sudo()

        message = body.get('message')
        project.write({'status': status, 'error_message': message})

    @api.model
    def create(self, vals):
       
        if 'criteria_weights' in vals and isinstance(vals['criteria_weights'], dict):
            vals['criteria_weights'] = json.dumps(vals['criteria_weights']) 
        return super(Projects, self).create(vals)

    def write(self, vals):
        if 'criteria_weights' in vals and isinstance(vals['criteria_weights'], dict):
            vals['criteria_weights'] = json.dumps(vals['criteria_weights']) 
        return super(Projects, self).write(vals)
           