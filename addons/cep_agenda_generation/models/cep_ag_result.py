from odoo import models, fields, api
import logging
import json
import threading
_logger = logging.getLogger(__name__)

class AgResult(models.Model):
    _name = 'cep.ag.result'
    _description = 'Result'

    title = fields.Char(string="Title", required=True)
    column_a = fields.Char(string="Column A", )
    column_b = fields.Char(string="Column B",)
    description = fields.Text(string="Description")
    keyword = fields.Char(string="Keyword")
    project_id = fields.Many2one('cep.ag.project', string="Project", required=True, ondelete='cascade')
    year = fields.Integer(string="Year")
    type = fields.Selection([
        ('dilemma', 'Dilemma'),
        ('agenda', 'Agenda'),
        ('frequency_analysis', 'Frequency Analysis'),
        ('n_gram', 'N Gram'),
    ], string="Type", required=True)
    metadata = fields.Text(string="Metadata")
    priority = fields.Float(string="Priority", default=0)
    parent_id = fields.Many2one('cep.ag.result', string="Parent Agenda", required=False, )
    dilemma_ids = fields.One2many('cep.ag.result', 'parent_id', string="Child Dilemmas", required=False, )
    attachment = fields.Binary(string="Attachment", required=False, attachment=False)
    dilemma_serial_id = fields.Integer(string="Dilemma Serial", required=False, index=True, help="Unique identifier to link with cep.ag.dilemma model's dilemma_serial_id field")
    related_dilemma_id = fields.Many2one('cep.ag.dilemma', string='Related Dilemma', compute='_compute_related_dilemma')

    @api.depends('dilemma_serial_id')
    def _compute_related_dilemma(self):
        for record in self:
            if record.dilemma_serial_id:
                dilemma = self.env['cep.ag.dilemma'].sudo().search([('dilemma_serial_id', '=', record.dilemma_serial_id)], limit=1)
                record.related_dilemma_id = dilemma.id if dilemma else False
            else:
                record.related_dilemma_id = False

    def get_dilemma_questions(self):
        self.ensure_one()
        if self.related_dilemma_id:
            return self.related_dilemma_id.questions
        return '{}'
    @api.model
    def create(self, vals_list):
        # Serialize the `metadata` field if it is a Python dict
        if 'metadata' in vals_list and isinstance(vals_list['metadata'], dict):
            vals_list['metadata'] = json.dumps(vals_list['metadata'])
        return super(AgResult, self).create(vals_list)

    def write(self, vals_list):
        # Serialize the `metadata` field if it is a Python dict
        if 'metadata' in vals_list and isinstance(vals_list['metadata'], dict):
            vals_list['metadata'] = json.dumps(vals_list['metadata'])
        return super(AgResult, self).write(vals_list)
     
    def save_dillemas(self,  body):
        result = body.get("result")
        type = result.get('type')
        results = result.get('data')
        project_id = body.get('project_id')
         
        for result in results:
            title = result.get('title')
            column_a = result.get('column_a')
            column_b = result.get('column_b')
            description = result.get('description')
            keyword = result.get('keyword')
            metadata = result.get('metadata')
            dilemma_serial_id = result.get('dilemma_serial_id')
        
            self.env['cep.ag.result'].sudo().create({
                'project_id': project_id,
                'title': title,
                'column_a': column_a,
                'column_b': column_b,
                'description': description,
                'keyword': keyword,
                'type': type,
                'metadata': metadata,
                'dilemma_serial_id': dilemma_serial_id
            })
        self.get_pdf_refs(project_id)
        
        
            
    def save_agendas(self,  body):
        try:
            result = body.get("result")
            type = result.get('type')
            results = result.get('data')
            project_id = body.get('project_id')
            status = body.get('status')
            project = self.env['cep.ag.project'].browse(int(project_id)).sudo()
        
            
            for result in results:
                title = result.get('title')
                dilemma_ids = result.get('dilemma_ids')
                agenda = self.env['cep.ag.result'].sudo().create({
                    'project_id': project_id,
                    'title': title,
                    'type': type ,
                    'dilemma_ids': [(6, 0, dilemma_ids)]
                })
               
               
                self.update_dilemmas_by_ids(dilemma_ids, {'parent_id': agenda.id})
                
                
            project.write({'status': status})
           
        except Exception as e:
            _logger.error(f"Error saving agendas: {str(e.with_traceback(None))}")
    
  
           
    def save_dilemma_reference(self, body):
       
        
        try:
            results = body.get("result")
            project_id = body.get('project_id')
            for result in results:
                id = result.get('id')
                metadata_dict = result.get('metadata')
                _logger.debug(metadata_dict)
                
                dilemma = self.env['cep.ag.result'].sudo().search([
                    ('id', '=', int(id)),
                ], limit=1)
                dilemma_metadata_dict  = json.loads(dilemma["metadata"])
                # Merge the dictionaries
                merged_metadata = self.deep_merge(dilemma_metadata_dict, metadata_dict)  # metadata_dict overrides dilemma_metadata_dict if keys overlap
            
                dilemma.write({'metadata': merged_metadata})
                
            self.generate_agenda(project_id)
        except Exception as e:
            _logger.error(f"Error saving references: {e.with_traceback(None)}")
    
  
   
    @api.model
    def delete_records_by_type_and_project(self, record_type, project_id):
        records_to_delete = self.env['cep.ag.result'].search([
            ('type', '=', record_type),
            ('project_id', '=', project_id)
        ])
        if records_to_delete:
            records_to_delete.unlink()
            return f"{len(records_to_delete)} record(s) deleted."
        return "No records found for the given type and project."
    @api.model
    def delete_records_by_project(self, project_id):
        
        records_to_delete = self.env['cep.ag.result'].search([
            ('project_id', '=', project_id)
        ])
        if records_to_delete:
            records_to_delete.unlink()
            return f"{len(records_to_delete)} record(s) deleted."
        return "No records found for the given type and project."
    
    @api.model
    def generate_agenda(self, project_id):
        
        dilemmas = self.env['cep.ag.result'].search([
             ('type', '=', 'dilemma'),
            ('project_id', '=', int(project_id))
        ]) 
        dilemma_data = []
        for dilemma in dilemmas: 
            dilemma_data.append({
                "id" : dilemma.id,
                "title" : dilemma.title,
                "description" : dilemma.description,
                "column_a" : dilemma.column_a,
                "column_b" : dilemma.column_b
            })
            
        mq = self.env['rabbitmq.server'].sudo().search([], limit=1)
        if not mq:
            return {'error': 'RabbitMQ server configuration not found.'}

        mq.publish_message(body={ 'event': 'PROCESS_AGENDA', 'project_id': project_id, 'dilemmas': dilemma_data,}, queue_name="AGENDA_SERVICE")

    
    def update_dilemmas_by_ids(self, ids, new_values):
        records = self.env['cep.ag.result'].sudo().browse(ids)
        
        if records:
            records.write(new_values)
            return {'status': True, 'message': 'Records updated successfully'}
        return {'status': False, 'message': 'No records found'}
    
    @api.model
    def get_pdf_refs(self, project_id):
        dilemmas = self.env['cep.ag.result'].search([
             ('type', '=', 'dilemma'),
            ('project_id', '=', int(project_id))
        ]) 
        pdf = self.env['cep.ag.agenda_source'].search([
             ('project_id', '=', int(project_id)),
             ('type', '=', 'pdf')
        ], limit=1)
        
        # If no PDF source or no attachments, proceed directly to agenda generation
        if not pdf or not pdf.attachment_ids:
            return self.generate_agenda(project_id)
            
        attachments = []
        for attachment in pdf.attachment_ids:
                # base_url = "https://1917-103-142-80-37.ngrok-free.app"
                base_url =  self.env['ir.config_parameter'].sudo().get_param('web.base.url')
                url = f"{base_url}/web/content/{attachment.id}?download=true"
                filename = attachment.name  # Get the filename from the attachment object
                attachments.append({"url": url, "filename": filename})

        pdf_data = {
            "id" : pdf.id,
            "attachments" : attachments
        }

        dilemma_data = []
        for dilemma in dilemmas: 
            dilemma_data.append({
                "id" : dilemma.id,
                "column_a" : dilemma.column_a,
                "column_b" : dilemma.column_b
            })
            
               
        mq = self.env['rabbitmq.server'].sudo().search([], limit=1)
        if not mq:
            return {'error': 'RabbitMQ server configuration not found.'}

        mq.publish_message(body={ 'event': 'GET_PDF_REFERENCE', 'project_id': project_id, 'dilemmas': dilemma_data, 'pdf': pdf_data}, queue_name="AGENDA_SERVICE")
        
        
    def deep_merge(self, dict1, dict2):
        result = dict1.copy()
        for key, value in dict2.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self.deep_merge(result[key], value)
            else:
                result[key] = value
        return result

    def save_n_gram(self, body):
        result = body.get("result")
        type = result.get('type')
        attachment = result.get('data')
        project_id = body.get('project_id')
        self.env['cep.ag.result'].sudo().create({
            'project_id': project_id,
            'title': "N Gram Analysis image",
            'type': type,
            'attachment': attachment
        })
            
    def save_frequency_analysis(self, body):
        result = body.get("result")
        type = result.get('type')
        attachments = result.get('data')
        project_id = body.get('project_id')
        
        for attachment in attachments:
            self.env['cep.ag.result'].sudo().create({
                'project_id': project_id,
                'title': "Frequency Analysis image",
                'type': type,
                'attachment': attachment
            })
        project = self.env['cep.ag.project'].browse(int(project_id)).sudo()
        project.write({'analyzer_status': 'completed'})
   