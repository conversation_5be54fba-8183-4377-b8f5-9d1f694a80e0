from odoo import models, fields, api
from datetime import datetime, timedelta
import json

class AgendaSource(models.Model):
    _name = 'cep.ag.agenda_source'
    _description = 'Agenda Source'

    project_id = fields.Many2one('cep.ag.project', string="Project", required=True, ondelete='cascade')
    type = fields.Selection([
        ('social_media', 'Social Media'),
        ('printed_media', 'Printed Media'),
        ('pdf', 'PDF'),
    ], string="Type", required=True)
    urls = fields.Text(string="URLs")  
    attachment_ids = fields.One2many(
        'ir.attachment', 'res_id', domain=[('res_model', '=', 'cep.ag.agenda_source')],
        string='Attachments', ondelete='cascade')

    @api.model
    def create(self, vals_list):
        # Serialize the `urls` field if it is a Python list
        if 'urls' in vals_list and isinstance(vals_list['urls'], list):
            vals_list['urls'] = json.dumps(vals_list['urls'])
        return super(AgendaSource, self).create(vals_list)

    def write(self, vals):
        # Serialize the `urls` field if it is a Python list
        if 'urls' in vals and isinstance(vals['urls'], list):
            vals['urls'] = json.dumps(vals['urls'])
        return super(AgendaSource, self).write(vals)