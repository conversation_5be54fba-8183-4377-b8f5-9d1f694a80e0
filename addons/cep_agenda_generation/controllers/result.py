from odoo import http
from odoo.http import request
import json
import base64
import logging
import os
import requests

_logger = logging.getLogger(__name__)

def isAuthenticate():
    return request.env.user.id != request.website.user_id


def isAgProject(owner_id):
    return owner_id.id == request.env.user.id


class AGController(http.Controller):

    _root_url = '/cep_agenda_generation'
    
    @http.route(f'{_root_url}/<string:id>/output', type='http', auth='user', methods=['GET'], website=True)
    def get_output(self, id,):
        if not isAuthenticate():
            return request.redirect('/web/login')
        project = request.env['cep.ag.project'].sudo().search([('id', '=', int(id))], limit=1)
        criteria_weights = project.criteria_weights if isinstance(project.criteria_weights, dict) else json.loads(project.criteria_weights or '{"env": 0, "eco": 0, "soc": 0, "tech": 0}')
        return request.render('cep_agenda_generation.output', {"project": project, 'criteria_weights': criteria_weights})

    @http.route(f'{_root_url}/<string:id>/results', type='http', auth='user', methods=['GET'], website=True)
    def get_results(self, id,):
        if not isAuthenticate():
            return request.redirect('/web/login')
        
        project = request.env['cep.ag.project'].sudo().search([('id', '=', int(id))], limit=1)
        
        if not project:
            return json.dumps({'status': False, 'message': 'Project not found'})

        # Convert project to dictionary
        project_data = {
            'id': project.id,
            'title': project.title,
            'status': project.status,
            'description': project.description,
            'analyzer_status': project.analyzer_status,
            'criteria_weights': project.criteria_weights if isinstance(project.criteria_weights, dict) else json.loads(project.criteria_weights or '{"env": 0, "eco": 0, "soc": 0, "tech": 0}'),
        }
        

        # 🛠 Fetch all related results in a single query
        all_results = request.env['cep.ag.result'].sudo().search([('project_id', '=', int(id))])

        # 🛠 Separate results into dilemma and agenda in Python
        dilemmas = []
        agendas = []
        n_gram = {}

        for result in all_results:
            result_data = {
                'id': result.id,
                'title': result.title,
                'description': result.description,
                'type': result.type,
                'priority': result.priority,
                'attachment': result.attachment.decode('utf-8') if result.attachment else None
            }
            if result.type == 'n_gram':
                n_gram = result_data
                
            if result.type == 'dilemma':
                questions = result.get_dilemma_questions()
                result_data['questions'] = questions 
                dilemmas.append(result_data)
            elif result.type == 'agenda':
                agenda_dilemmas = []
                pdf_list = []
                printed_media_list = []
                social_media_list = []
                for dilemma in result.dilemma_ids:
                    agenda_dilemmas.append({
                        'id': dilemma.id,
                        'title': dilemma.title,
                    })
                    if "metadata" in dilemma :
                        metadata = json.loads(dilemma["metadata"])
                        if "reference" in metadata: 
                            if "pdf" in metadata["reference"]:
                                pdf_list.append(metadata["reference"]["pdf"])
                
                            if "social_media" in metadata["reference"] and metadata["reference"]["social_media"] is not None:
                                social_media_list.append(metadata["reference"]["social_media"]["url"])

                            if "printed_media" in metadata["reference"] and metadata["reference"]["printed_media"] is not None:
                                printed_media_list.append(metadata["reference"]["printed_media"]["url"])
                
                unique_pdfs = set(pdf_list)
                
           
                unique_social_media = set(social_media_list)
                unique_printed_media = set(printed_media_list)
                
                result_data['reference'] = {'pdf': sorted(unique_pdfs), 'social_media': sorted(unique_social_media), 'printed_media': sorted(unique_printed_media)}
                result_data['dilemmas'] = agenda_dilemmas
                agendas.append(result_data)
        
        # Sort dilemmas by priority (ascending order)
        dilemmas.sort(key=lambda x: x['priority'], reverse=True)
        data = {
            'dilemma': dilemmas,
            'agenda': agendas,
            'project': project_data,
            'n_gram': n_gram
        }
        
        return json.dumps({'status': True, 'data': data})
            
      
    @http.route(f'{_root_url}/<string:id>/prioritize', type='http', auth='user', methods=['POST'], website=True, csrf=False)
    def prioritize(self, id,):
        if not isAuthenticate():
            return request.redirect('/web/login')
        
        try:
            data = json.loads(request.httprequest.data)
        except Exception as e:
            return json.dumps({
                    'status': False,
                    'message': 'Invalid JSON payload'
                })

        env = data.get('env')
        eco = data.get('eco')
        soc = data.get('soc')
        tech = data.get('tech')
        
        project = request.env['cep.ag.project'].sudo().search([('id', '=', int(id))], limit=1)
        
        if not project:
            return json.dumps({'status': False, 'message': 'Project not found'})

        # 🛠 Fetch all related results in a single query
        dilemmas = request.env['cep.ag.result'].sudo().search([('type', '=', 'dilemma'),  ('project_id', '=', int(id))])
        

     
        linear_sum_payload = {
            'env': [],
            'eco': [],
            'soc': [],
            'tech': []
        }

        for dilemma in dilemmas:
            metadata = json.loads(dilemma.metadata)
            priority_data = metadata.get('priority_data')
            linear_sum_payload["env"].append({"option_no": dilemma.id, "value": priority_data.get('ENV')})
            linear_sum_payload["eco"].append({"option_no": dilemma.id, "value": priority_data.get('ECO')})
            linear_sum_payload["soc"].append({"option_no": dilemma.id, "value": priority_data.get('SOC')})
            linear_sum_payload["tech"].append({"option_no": dilemma.id, "value": priority_data.get('TECH')})
        
        linear_sum_scores = []
        weight = 0
        for key, value in linear_sum_payload.items():
            
            if key == 'env':
                weight = env
            elif key == 'eco':
                weight = eco
            elif key == 'soc':
                weight = soc
            elif key == 'tech':
                weight = tech
                
            payload = {
                'criteria': key,
                'children': [
                    {
                        'weight': weight,
                        'op_values': self.linear_sum(value, 'beneficial')
                    }
                ]
            }
            linear_sum_scores.append(payload)
        
        final, analysis_result = self.analysis_data(linear_sum_scores)
        project.write({
            'criteria_weights': {
                'env': round(float(env), 2),
                'eco': round(float(eco), 2),
                'soc': round(float(soc), 2),
                'tech': round(float(tech), 2)
            }
        })
        for result in analysis_result:
            dilemma = request.env['cep.ag.result'].sudo().search([('id', '=', result['option_no'])], limit=1)
            dilemma.write({'priority': result['value']})
        
        return json.dumps({'status': True, 'message': 'Dilemmas prioritized successfully'})
        
        
    def sum_of_values_in_array(self, array_input):
        return sum(float(value) for value in array_input)

    def sum_of_inverse_values_in_array(self, array_input):
        return sum(1 / float(value) for value in array_input)

    def linear_sum(self, op_values, attribute):
        sum_of_opt_value = self.sum_of_values_in_array([item['value'] for item in op_values])
        sum_of_inverse_opt_value = self.sum_of_inverse_values_in_array([item['value'] for item in op_values])
        
        result = []
        
        for op_value in op_values:
            calculated_result = None
            
            # IF attribute is beneficial
            if attribute == 'beneficial':
                calculated_result = op_value['value'] / sum_of_opt_value
            
            # IF attribute is non-beneficial
            elif attribute == 'non_beneficial':
                calculated_result = (1 / op_value['value']) / sum_of_inverse_opt_value
            
            result.append({**op_value, 'value': round(calculated_result, 4)})
        
        return result

    def analysis_data(self, data):
        final_result = []
        summed_values_dict = {}
        
        for item in data:
            print(f'max array length - {max(len(child["op_values"]) for child in item["children"])}')
            
            multiply_weight_value_arr = []
            for child in item['children']:
                for op_value in child['op_values']:
                    weighted_value = round(op_value['value'] * child['weight'], 4)
                    multiply_weight_value_arr.append({'option_no': op_value['option_no'], 'value': weighted_value})
                    summed_values_dict[op_value['option_no']] = summed_values_dict.get(op_value['option_no'], 0) + weighted_value
            
            result = {'criteria': item['criteria'], 'op_values': multiply_weight_value_arr}
        
            final_result.append(result)
        
        summed_values_arr = [{'option_no': k, 'value': round(v, 4)} for k, v in summed_values_dict.items()]
        
        return final_result, summed_values_arr
    
    
    @http.route(f'{_root_url}/<string:id>/check', type='http', auth='user', methods=['GET'], website=True)
    def check(self, id,):
        if not isAuthenticate():
            return request.redirect('/web/login')
        
        
        request.env['cep.ag.result'].sudo().generate_agenda(project_id=id)
        return json.dumps({'status': True, 'message': 'Agenda generated successfully'})
    
    @http.route(f'{_root_url}/<string:id>/frequency', type='http', auth='user', methods=['GET'], website=True)
    def get_frequency(self, id,):
        if not isAuthenticate():
            return request.redirect('/web/login')
        
        project = request.env['cep.ag.project'].sudo().search([('id', '=', int(id))], limit=1)
        
        if not project:
            return json.dumps({'status': False, 'message': 'Project not found'})

        # Fetch all frequency results
        frequency_results = request.env['cep.ag.result'].sudo().search([('project_id', '=', int(id)), ('type', '=', 'frequency_analysis')])
        
        if not frequency_results:
            return json.dumps({'status': project.analyzer_status,  'message': 'Frequency data not found'})

        results_data = []
        for result in frequency_results:
            results_data.append({
                'id': result.id,
                'title': result.title,
                'description': result.description,
                'type': result.type,
                'attachment': result.attachment.decode('utf-8') if result.attachment else None
            })
        
        return json.dumps({
            'status': project.analyzer_status, 
            'data': results_data, 
        })