<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <template id="cep_agenda_generation.output" name="Agenda Generation Output">
        <t t-call="website.layout">
            <t t-set="head">
                <!-- Include custom assets -->
                <t t-call-assets="web.assets_common" />

                <t t-call-assets="web.assets_frontend" />
                <t t-call-assets="cep_agenda_generation.ag_assets" />
                <t t-call-assets="cep_agenda_generation.ag_results" />

            </t>
            <t t-set="title"> Agenda Dashboard</t>
            <div id="wrapwrap" class="homepage">


                <aside class="sidebar">
                    <div class="climas-logo">
                        <img src="/cep_agenda_generation/static/src/img/CLIMAS_logo.png"
                            alt="CLIMAS Logo" />
                    </div>
                    <div class="header">
                        <h4>AGENDA</h4>

                    </div>
                    <div class="divider"></div>
                    <div class="menu">
                        <a href="/cep_agenda_generation" class="menu-item active"><i
                                class="fa fa-home"></i> Home</a>
                        <div class="menu-item-group">
                            <a href="#" class="menu-item has-submenu" id="create-project-menu">
                                <i class="fa fa-plus"></i> Create Agenda <i
                                    class="fa fa-chevron-down submenu-icon"></i>
                            </a>
                            <div class="submenu">
                                <a
                                    href="/cep_agenda_generation/create_project?type=climate_assembly"
                                    class="submenu-item">
                                    <i class="fa fa-circle-o"></i> Climate Assembly </a>
                                <a
                                    href="/cep_agenda_generation/create_project?type=climate_dialogue"
                                    class="submenu-item">
                                    <i class="fa fa-circle-o"></i> Climate Dialogue </a>
                                <a
                                    href="/cep_agenda_generation/create_project?type=just_transition_dialogue"
                                    class="submenu-item">
                                    <i class="fa fa-circle-o"></i> Just Transition Dialogue </a>
                            </div>
                        </div>
                        <a href="/cep_agenda_generation/list" class="menu-item"><i
                                class="fa fa-save"></i> Project list</a>
                        <a href="/cep_agenda_generation/project/favorites" class="menu-item"><i
                                class="fa fa-heart"></i> Favorites</a>
                    </div>
                </aside>


                <!-- Button to toggle sidebar for small screens -->
                <button class="sidebar-toggle" id="sidebar-toggle">
                    <i id="sidebar-icon" class="fa fa-bars"></i>
                </button>

                <!-- Main Content -->
                <main class="main-content output-main-content">
                    <div class="container-fluid">
                        <div class="row flex-wrap-reverse flex-lg-nowrap">
                            <!-- Contents -->
                            <div class="col-12 col-lg-9">
                                <!-- Header Section -->
                                <div class="row">
                                    <h4 class="heading-3">Project: <t t-esc="project.title" /></h4>
                                    <p>
                                        <t t-esc="project.description" />
                                    </p>
                                    <hr />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="container-fluid" id="main-content">
                        <div class="row flex-wrap-reverse flex-lg-nowrap">
                            <!-- Contents -->
                            <div class="col-12 col-lg-9">
                                <!-- Header Section -->
                                <div class="row">
                                    <h3 class="heading-4" id="output-header">Output</h3>
                                </div>
                                <input type="hidden" id="project_id" t-att-value="project.id" />
                                <input type="hidden" id="project_status"
                                    t-att-value="project.status" />

                                <!-- List of Dilemmas -->
                                <section id="dilemmas" class="row mt-5">
                                    <div class="col-12">
                                        <h5 class="header">List of Dilemmas</h5>
                                        <ol class="list-group list-group-numbered"
                                            id="dilemmas-list">


                                        </ol>
                                        <div class="row">
                                            <div id="dilemma-spinner"></div>
                                            <!-- <div class="text-primary text-center m-auto fade show"
                                                id="dilemma-spinner">
                                                <h5>Dilemma is Processing<span class="loading-dots">
                                                    ...</span></h5>
                                            </div> -->
                                        </div>
                                    </div>

                                    
                                    <div class="col-12 mt-3 d-none" id="dilemma_priritise">
                                        <p class="text-dark">Do you want to prioritise the list of
                                            dilemmas under
                                            criteria?</p>
                                        <!-- Proceed Button -->
                                        <button type="submit" class="btn btn-secondary"
                                            data-bs-toggle="modal"
                                            data-bs-target="#prioritize-modal">
                                            Prioritise
                                        </button>
                                    </div>
                                </section>


                                <!-- List of Agendas -->
                                <section id="agendas" class="row mt-4">
                                    <h5 class="header">List of Agendas</h5>
                                    <div class="accordion" id="agendaAccordion">

                                    </div>

                                    <!-- <div class="text-primary text-center m-auto fade show"
                                        id="agenda-spinner">
                                        <h5>Agenda is Processing<span class="loading-dots">...</span></h5>
                                    </div> -->
                                </section>

                                <!-- N-Gram Analysis Section -->
                                <section id="words_frequency" class="row mt-4">
                                    <h5 class="header">Most common words frequency</h5>
                                    <div class="col-12">
                                        <div id="ngram-container">
                                            
                                        </div>
                                    </div>
                                </section>

                                <!-- Keyword Frequency Section -->
                                <section id="trends" class="row mt-3">
                                    <h5 class="header">Keyword Frequency Over Years</h5>
                                    <div class="col-12">
                                        <div class="mb-3">
                                            <button type="button" class="btn btn-secondary" id="get-frequency-btn">
                                                Get Frequency
                                            </button>
                                        </div>
                                        <div id="frequency-container">
                                            
                                        </div>
                                    </div>
                                </section>
                                <section id="trends" class="row mt-3">
                                    <h5 class="header">Sentiment analysis</h5>
                                    <!-- <div class="col-12">
                                        <div class="mb-3">
                                            <button type="button" class="btn btn-secondary" id="get-frequency-btn">
                                                Get Frequency
                                            </button>
                                        </div>
                                        <div id="frequency-container">
                                            
                                        </div>
                                    </div> -->
                                </section>

                            </div>







                            
                            <!-- Sidebar Section -->
                            <div
                                class="col-12 col-lg-3 sidebar-container shadow px-3 py-4 d-none d-lg-block">
                                <!-- Navigation List -->
                                <nav>
                                    <ul class="nav flex-column">
                                        <li class="nav-item mb-2">
                                            <a class="nav-link text-dark" href="#agendas">
                                                <i class="fa fa-list-alt me-2"></i> List of Agendas </a>
                                        </li>
                                        <li class="nav-item mb-2">
                                            <a class="nav-link text-dark" href="#dilemmas">
                                                <i class="fa fa-question-circle me-2"></i> List of
                                                Dilemmas </a>
                                        </li>
                                        <li class="nav-item mb-2">
                                            <a class="nav-link text-dark" href="#words_frequency">
                                                <i class="fa fa-line-chart me-2"></i> Most common words frequency </a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link text-dark" href="#trends">
                                                <i class="fa fa-bar-chart me-2"></i> Keyword Frequency Over Years </a>
                                        </li>
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div>

                </main>
            </div>

        </t>

        <div class="modal fade" id="prioritize-modal" tabindex="-1"
            aria-labelledby="prioritize-modal-label"
            aria-hidden="true">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h1 class="modal-title fs-5">Prioritise</h1>
                    </div>

                    <div class="modal-body">
                        <div class="container">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="row ">
                                        <label class="criteria-header">Criterion</label>
                                        <div class="d-flex flex-column flex-lg-row gap-3">
                                            <input type="text" class="form-control"
                                                value="Environmental Impact"
                                                readonly="readonly" />
                                            <div
                                                class="options d-flex flex-column flex-lg-row gap-2">
                                                <input type="number" id="criteria1"
                                                    class="form-control criteria-weight-input"
                                                    placeholder="Enter weight"
                                                    t-att-value="criteria_weights and criteria_weights.get('env', '')" />
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row mt-3">
                                        <label class="criteria-header">Criterion</label>
                                        <div class="d-flex flex-column flex-lg-row gap-3">
                                            <input type="text" class="form-control"
                                                value="Economic Factors"
                                                readonly="readonly" />
                                            <div
                                                class="options d-flex flex-column flex-lg-row gap-2">
                                                <input type="number" id="criteria2"
                                                    class="form-control criteria-weight-input"
                                                    placeholder="Enter weight"
                                                    t-att-value="criteria_weights and criteria_weights.get('eco', '')" />
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row mt-3">
                                        <label class="criteria-header">Criterion</label>
                                        <div class="d-flex flex-column flex-lg-row gap-3">
                                            <input type="text" class="form-control"
                                                value="Social Equity"
                                                readonly="readonly" />
                                            <div
                                                class="options d-flex flex-column flex-lg-row gap-2">
                                                <input type="number" id="criteria3"
                                                    class="form-control criteria-weight-input"
                                                    placeholder="Enter weight"
                                                    t-att-value="criteria_weights and criteria_weights.get('soc', '')" />
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row mt-3">
                                        <label for="criteria4" class="criteria-header">Criterion</label>
                                        <div class="d-flex flex-column flex-lg-row gap-3">
                                            <input type="text" class="form-control"
                                                value="Technology &amp; Innovation"
                                                readonly="readonly" />
                                            <div
                                                class="options d-flex flex-column flex-lg-row gap-2">
                                                <input type="number" id="criteria4"
                                                    class="form-control criteria-weight-input"
                                                    placeholder="Enter weight"
                                                    t-att-value="criteria_weights and criteria_weights.get('tech', '')" />
                                            </div>
                                        </div>
                                    </div>

                                </div>

                                <div class="col-md-4">

                                    <div class="card instruction-box p-3 mt-3"
                                        style="background-color: #e7f1ff; 
            border: 2px solid #e7f1ff; 
            border-radius: 5px;">
                                        <h5 class="card-title">Instructions</h5>
                                        <ol>

                                            <li class="mb-1">
                                                Prioritise according to your criteria.


                                            </li>
                                            <li class="mb-1">
                                                <strong>0.9</strong> represents the <strong>maximum
                                                impact</strong> that should be Prioritised, while <strong>
                                                0.1</strong> indicates <strong>minimum
                                                    impact</strong> according to the criteria. </li>
                                            <li class="mb-1">
                                                <strong> Note: The sum of all the scores of criteria
                                                    must equal 1.</strong>

                                            </li>

                                        </ol>
                                    </div>

                                </div>
                            </div>


                        </div>

                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary text-white"
                            id="submit-prioritize">Output</button>
                    </div>
                </div>
            </div>
        </div>

    </template>
</odoo>