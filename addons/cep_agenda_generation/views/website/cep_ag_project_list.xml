<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <template id="cep_agenda_generation.project_list" name="Agenda Generation Project list">
        <t t-call="website.layout">
            <t t-set="head">
                <!-- Include custom assets -->
                <t t-call-assets="web.assets_common"/>

                <t t-call-assets="web.assets_frontend"/>
                <t t-call-assets="cep_agenda_generation.ag_assets"/>
                <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet"/>

            </t>
            <t t-set="title"> Agenda Dashboard</t>
            <div id="wrapwrap" class="homepage">

                <aside class="sidebar">
                    <div class="climas-logo">
                        <img src="/cep_agenda_generation/static/src/img/CLIMAS_logo.png" alt="CLIMAS Logo"/>
                    </div>
                    <div class="header">
                        <h4>AGENDA</h4>
                        
                    </div>
                    <div class="divider"></div>
                    <div class="menu">
                        <a href="/cep_agenda_generation" class="menu-item "><i class="fa fa-home"></i> Home</a>
                        <div class="menu-item-group">
                            <a href="#" class="menu-item has-submenu" id="create-project-menu">
                                <i class="fa fa-plus"></i> Create Agenda
                                <i class="fa fa-chevron-down submenu-icon"></i>
                            </a>
                            <div class="submenu">
                                <a href="/cep_agenda_generation/create_project?type=climate_assembly" class="submenu-item">
                                    <i class="fa fa-circle-o"></i> Climate Assembly
                                </a>
                                <a href="/cep_agenda_generation/create_project?type=climate_dialogue" class="submenu-item">
                                    <i class="fa fa-circle-o"></i> Climate Dialogue
                                </a>
                                <a href="/cep_agenda_generation/create_project?type=just_transition_dialogue" class="submenu-item">
                                    <i class="fa fa-circle-o"></i> Just Transition Dialogue
                                </a>
                            </div>
                        </div>
                        <a href="/cep_agenda_generation/list" class="menu-item active"><i class="fa fa-save"></i>
                            Project list</a>
                          <a href="/cep_agenda_generation/project/favorites" class="menu-item "><i class="fa fa-heart"></i> Favorites</a>
                    </div>
                </aside>

                <!-- Button to toggle sidebar for small screens -->
                <button class="sidebar-toggle" id="sidebar-toggle">
                    <i id="sidebar-icon" class="fa fa-bars"></i>
                </button>

                <!-- Main Content -->
                <main class="main-content listPage">
                    <div class="container-fluid">
                        <div class="row">
                            <h4 class="heading-4">Saved Projects</h4>
                        </div>
                        <div class="row">
                            <!-- first card -->

                            <section class="container my-2">
                                <hr class="border-primary"/>
                                <div class="table-responsive">
                                    <table id="portal-table" class="table agenda-table table-striped shadow"
                                        
                                        >
                                        <thead>
                                            <tr>

                                                <th class="title-cell">SL</th>
                                                <th class="title-cell">Title</th>
                                                <th class="title-cell">Category</th>
                                                <th class="title-cell">Status</th>
                                                <th class="title-cell">Error Reason</th>
                                                <th class="title-cell">Create Date</th>
                                                <th >Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <t t-if="projects">
                                                <t t-foreach="projects" t-as="project">
                                                    <tr>
                                                        <td style="font-weight:bold" class="title-cell">
                                                            <t t-esc="project_index + 1"/>
                                                        </td>
                                                        <td class="title-cell">
                                                            <a class="title"
                                                                t-att-href="'/cep_agenda_generation/' + str(project.id) + '/view'">
                                                                <t t-esc="project.title"/>
                                                            </a>
                                                        </td>
                                                        <td class="title-cell">
                                                            <t t-esc="project.category.replace('_', ' ').title()"/>
                                                        </td>
                                                        <td class="title-cell">
                                                            <span
                                                                t-att-class="'badge table-badge ' + {
                                                                                        'draft': 'bg-secondary',
                                                                                        'in_progress': 'bg-info',
                                                                                        'completed': 'bg-success',
                                                                                        'failed': 'bg-danger'
                                                                                    }.get(project.status, 'bg-dark')">
                                                                <t
                                                                    t-esc="dict(project._fields['status'].selection).get(project.status)"/>
                                                            </span>
                                                        </td>

                                                        <td class="title-cell">
                                                            <p class="mb-0">
                                                                <t t-esc="project.error_message or '-'"/>
                                                            </p>
                                                        </td>
                                                        <td class="title-cell">
                                                            <t t-esc="project.create_date.strftime('%Y-%m-%d')"/>
                                                        </td>

                                                        <td >
                                                            <a t-att-href="'/cep_agenda_generation/%d/view' % project.id"
                                                                class="btn list-btn btn-primary me-1">
                                                                <span>Edit</span>
                                                            </a>
                                                            <a
                                                                t-att-href="'/cep_agenda_generation/%d/output' % project.id if project.status == 'completed' else '#'"
                                                                t-att-class="'btn me-1 ' + ('btn-secondary' if project.status == 'completed' else 'btn-secondary disabled')"
                                                                t-att-disabled="project.status != 'completed'">
                                                                <span>View results</span>
                                                            </a>
                                                            <a t-att-href="'/cep_agenda_generation/%d/delete' % project.id"
                                                                class="btn btn-danger delete-project" data-bs-toggle="modal"
                                                                data-bs-target="#delete-ag-project-modal">
                                                                <span>Delete</span>
                                                            </a>
                                                        </td>

                                                    </tr>
                                                </t>
                                            </t>
                                        </tbody>
                                    </table>
                                </div>
                            </section>


                        </div>
                    </div>

                    <div class="modal fade" id="delete-ag-project-modal" tabindex="-1"
                        aria-labelledby="delete-ag-project-modal-label" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h1 class="modal-title fs-5" id="delete-ag-project-modal-label">Project Delete Confirmation</h1>
                                </div>

                                <div class="modal-body"> Are you sure you want to delete this Project <span></span>? </div>

                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">No</button>
                                    <a href="" type="button" class="btn btn-primary" id="delete-ag-project-modal-yes">Yes</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- DataTables JS -->
                    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
                    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
                </main>


            </div>
        </t>
    </template>
</odoo>
