.new-project-main-content {
  /* Tab navs */
  .nav-tabs {
    display: flex;
    align-items: center;
    gap: 10px;
    border: none;
  }

  .nav-tabs .nav-item {
    margin-bottom: 0;
  }

  .nav-tabs .nav-link {
    font-size: 16px;
    font-weight: 600;
    color: #155493;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    border-radius: 0;
    transition: color 0.3s, border-color 0.3s;
  }

  .nav-tabs .nav-link i {
    font-size: 18px;
  }

  .nav-tabs .nav-link:hover {
    color: #249afb;
  }

  .nav-tabs .nav-link.active {
    color: #249afb;
    border-bottom: 2px solid #249afb;
  }

  .tab-content {
    padding: 20px 0;
  }

  /* tab contents  */

  /* common section styles */
  .keywords-section,
  .print-media-section {
    /* max-width: 332px; */
    max-width: 1407px;
  }

  /* Section Heading */
  .section-heading {
    font-size: 15px;
    font-weight: 500;
    line-height: 24px;
    color: #0f182a;
    margin-bottom: 16px;
  }

  /* Search Row Container */
  .search-row-container {
    display: grid;
    grid-gap: 10px;
    grid-template-columns: 1fr 50px;
  }
  /* Search Row */

  .search-row {
    display: flex;
    align-items: normal;
    gap: 8px;
    flex-direction: column;
  }

  .print-media-search-box,
  .search-box {
    flex-grow: 1;
    padding: 8px 12px;
    font-size: 14px;
    border: 1px solid #cbd5e1;
    border-radius: 4px;
    width: 100%;
    outline: none;
  }

  .search-box:focus {
    border-color: #249afb;
    box-shadow: 0 0 4px rgba(36, 154, 251, 0.5);
  }

  .delete-btn,
  .add-btn {
    padding: 10px 12px;
    font-size: 20px;
    border-radius: 5px;
  }

  .add-btn {
    border: 1px solid #249afb;
    background-color: white;
    color: #249afb;

    display: flex;
    justify-content: center;
    align-items: center;
  }

  .add-btn i {
    font-size: 16px;
  }

  .add-btn:hover {
    background-color: #e2e8f0;
  }
  .tagify__tag-text {
    font-size: 15px;
    font-weight: 600;
  }

  /* Checkbox Section */
  /* .checkbox-section { */
  /* margin-top: 16px; */
  /* width: 100%; */
  /* } */

  .checkbox-row {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
  }

  /* Save Button */
  .save-btn,
  .proceed-btn {
    display: inline-block;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 500;
    color: white;
    background-color: #249afb;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    width: 100%;
    max-width: 250px;
    font-size: 16px;
    font-weight: 700;
    line-height: 24px;
  }

  .save-btn:hover,
  .proceed-btn:hover {
    background-color: #1976d2;
  }

  /* Keyword Section */
  .keywords-section {
  }

  /* General Section Styling */
  .academic-paper-section {
    padding: 16px;
    max-width: 1400px;
  }

  /* Form Group Styling */
  .form-group {
    margin-bottom: 16px;
  }

  /* Label Styling */
  .form-label {
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    color: #0f182a;
    display: block;
    margin-bottom: 8px;
  }

  /* Upload Input Field (hidden) */
  .upload-input {
    display: none;
    /* Hide the default file input */
  }

  /* Custom Upload Text */
  .upload-text {
    width: 100%;
    display: inline-block;
    padding: 8px 12px;
    font-size: 14px;
    border: 1px solid #cbd5e1;
    border-radius: 4px;
    color: #0f182a;
    cursor: pointer;
    background-color: #fff;
  }

  .upload-text:hover {
    background-color: #f0f0f0;
  }

  /* Upload Button */
  .upload-btn {
    padding: 8px;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
  }

  .upload-btn img {
    width: 36px;
    height: 27px;
  }

  /* Dropdown Styling */
  .dropdown {
    width: 100%;
    padding: 8px 12px;
    font-size: 14px;
    border: 1px solid #cbd5e1;
    border-radius: 4px;
    color: #0f182a;
    background-color: white;
  }

  /* Years Input Styling */
  .years-input-group {
    display: flex;
    gap: 8px;
  }

  .year-input {
    flex: 1;
    padding: 8px 12px;
    font-size: 14px;
    border: 1px solid #cbd5e1;
    border-radius: 4px;
    color: #0f182a;
  }

  .year-input::placeholder {
    color: #cbd5e1;
  }
}

.project-favorite,
.project-edit {
  border-radius: 22px !important;
  line-height: 15px;
  padding: 9px 10px;
}

@media screen and (max-width: 767px) {
  .main-content.homepage .badge {
    min-width: auto;
    height: 30px;
    padding: 8px 16px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 600;
    line-height: 24px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  th.title-cell,
  td.title-cell,
  td.title-cell a.title,
  td.title-cell p {
    font-size: 13px;
  }

  .main-content .table-badge {
    padding: 3px 11px;
    border-radius: 29px;
    font-size: 10px;
    font-weight: 600;
    line-height: 13px;
  }

  .instruction-box {
    margin-top: 20px;
  }

  .main-content.output-main-content .list-group-item {
    padding-left: 0;
    padding-right: 0;
  }
  .main-content.output-main-content {
    padding-left: 0;
    padding-right: 0;
  }
}
