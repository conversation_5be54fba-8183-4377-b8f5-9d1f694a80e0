/* General Styles */
.output-main-content {
  /* Chart Container */
  /* Year Selection Dropdown */
  /* Proceed But<PERSON> */
  /* Sidebar Container */
  /* Checkbox Group */
}

.output-main-content .header {
  font-size: 20px;
  font-weight: 600;
  color: #0f182a;
  margin-bottom: 16px;
}

.output-main-content .question-section .category-title {
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
}

.output-main-content .list-group-item {
  border: none;
}

.output-main-content .chart-container {
  position: relative;
  background-color: #fafcfd;
  border-radius: 5px;
  padding: 12px;
}

.output-main-content .year-select {
  background-image: url('../image/arrow_back.png');
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1rem 1rem;
  width: 150px;
  position: absolute;
  top: 24px;
  right: 12px;
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  color: #fff;
  background-color: #249afb;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.output-main-content .year-select option {
  background-color: #fff;
  color: #000;
}

.output-main-content .btn-secondary {
  width: 332px;
  display: inline-block;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: 700;
  line-height: 24px;
  color: #fff;
  background-color: #249afb;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.output-main-content .btn-secondary:hover {
  background-color: #1976d2;
}

.output-main-content .btn-outline-secondary {
  width: 332px;
  border: 1px solid #249afb;
  color: #249afb;
  border-radius: 6px;
  padding: 8px 16px;
  background: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}

.output-main-content .btn-outline-secondary:hover {
  background-color: #249afb;
  color: white;
}

.output-main-content .sidebar-container {
  position: fixed;
  height: fit-content;
  background-color: #fafcfd;
  top: 80px;
  right: 19px;
  overflow-y: auto;
  border-radius: 8px;
  max-width: 300px;
}

.output-main-content .sidebar-container .nav-link.active {
  color: #fff !important;
  background-color: #249afb;
  border-radius: 4px;
  font-weight: 600;
}

.output-main-content .checkbox-group .checkbox-label {
  display: flex;
  align-items: center;
}

.output-main-content .checkbox-group .custom-checkbox {
  margin-right: 8px;
  accent-color: #0f182a;
  border: 1px solid #cbd5e1;
  border-radius: 4px;
}
.output-main-content li.list-group-item {
  font-size: 16px;
}

.loading {
  display: inline-block;
  overflow: hidden;
  height: 1.3em;
  margin-top: -0.3em;
  line-height: 1.5em;
  vertical-align: text-bottom;
}

.loading::after {
  display: inline-table;
  white-space: pre;
  text-align: left;
}

/* default loading is ellip */
.loading::after {
  content: '\A.\A..\A...';
  animation: spin4 2s steps(4) infinite;
}

.loading.dots::after {
  content: '⠋\A⠙\A⠹\A⠸\A⠼\A⠴\A⠦\A⠧\A⠇\A⠏';
  animation: spin10 1s steps(10) infinite;
}

/* --- Animations --- */

@keyframes spin1 {
  to {
    transform: translateY(-1.5em);
  }
}
@keyframes spin2 {
  to {
    transform: translateY(-3em);
  }
}
@keyframes spin3 {
  to {
    transform: translateY(-4.5em);
  }
}
@keyframes spin4 {
  to {
    transform: translateY(-6em);
  }
}
@keyframes spin5 {
  to {
    transform: translateY(-7.5em);
  }
}
@keyframes spin6 {
  to {
    transform: translateY(-9em);
  }
}
@keyframes spin7 {
  to {
    transform: translateY(-10.5em);
  }
}
@keyframes spin8 {
  to {
    transform: translateY(-12em);
  }
}
@keyframes spin9 {
  to {
    transform: translateY(-13.5em);
  }
}
@keyframes spin10 {
  to {
    transform: translateY(-15em);
  }
}
@keyframes spin11 {
  to {
    transform: translateY(-16.5em);
  }
}
@keyframes spin12 {
  to {
    transform: translateY(-18em);
  }
}

button {
  font-family: monospace;
  margin-right: 1em;
  font-size: 1.5rem;
  padding: 0.8rem 1.5rem 0.7rem;
  border-width: 1px;
  border-style: solid;
  border-radius: 2px;
  background-color: #fff;
  border-color: #2196f3;
  color: #2196f3;
  line-height: 1;
}
