odoo.define('cep_agenda_generation.fetch_results', function (require) {
  const { _t } = require('web.core');
  $(document).ready(function () {
    $('.criteria-weight-input').on('change', function () {
      let value = parseFloat($(this).val()).toFixed(2);
      $(this).val(value);
    });

    

    const project_id = $('#project_id').val();
    const agenda_loader = $('#agenda-spinner');
    const dilemma_loader = $('#dilemma-spinner');

    const agendas_list = $('#agendaAccordion');
    const dilemmas_list = $('#dilemmas-list');
    dilemmas_list.addClass('accordion');
    const dilemma_priritise = $('#dilemma_priritise');
    const ngramContainer = $('#ngram-container');
    const createLoader = (message) => $('<div/>', {
      class: 'text-center',
      html: '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div><p class="mt-2">' + message + '</p>'
    });

    const dilemmaSpinner = createLoader(_t('Loading dilemmas...'));
    const agendaSpinner = createLoader(_t('Loading agendas...'));
    const ngramLoader = createLoader(_t('Loading N-gram visualization...'));

    dilemma_loader.append(dilemmaSpinner);
    agendas_list.append(agendaSpinner);
    ngramContainer.append(ngramLoader);

    function fetchResults() {
      axios
        .get(`/cep_agenda_generation/${project_id}/results`)
        .then((response) => {
          const data = response.data;

          // Check if the project status is completed
          if($('#dilemmas-list').children('.accordion-item').length == 0){
  
            if (data.data.dilemma.length > 0) {
              const showMoreBtn = $('<button/>', {
                class: 'mt-3 d-none',
                text: _t('Show More'),
                id: 'show-more-dilemmas',
                style: "font-size: 15px;padding: 8px 15px; margin-bottom: 50px;"
              });
              
              data.data.dilemma.forEach((dilemma, index) => {
                let questions = [];
                try {
                  questions = JSON.parse(dilemma.questions || '[]');
                  
                } catch (e) {
                  console.error('Error parsing questions:', e);
                  questions = [];
                }

                const listItem = $(`<div class="accordion-item ${index >= 15 ? 'd-none' : ''}" id="dilemma-${dilemma.id}">
                  <h2 class="accordion-header" id="dilemmaHeading${index}">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                      data-bs-target="#dilemmaCollapse${index}" aria-expanded="false"
                      aria-controls="dilemmaCollapse${index}">
                      ${dilemma.title}
                    </button>
                  </h2>
                  <div id="dilemmaCollapse${index}" class="accordion-collapse collapse"
                    aria-labelledby="dilemmaHeading${index}" data-bs-parent="#dilemmas-list">
                    <div class="accordion-body">
                      <p class="mb-3">${dilemma.description}</p>
                      ${questions.length > 0 && !(questions.length === 1 && questions[0] === "nan") ? `
                        <div class="questions-section">
                          <h6 class="fw-bold">${_t("Questions:")}</h6>
                          <ul class="list-unstyled">
                            ${questions.map(q => `<li class="mb-2">• ${q}</li>`).join('')}
                          </ul>
                        </div>
                      ` : ''}
                    </div>
                  </div>
                </div>`);
                dilemmas_list.append(listItem);
              });

              if (data.data.dilemma.length > 15) {
                showMoreBtn.removeClass('d-none');
                showMoreBtn.text(_t('Show More'));
                dilemmas_list.after(showMoreBtn);
              }

              showMoreBtn.on('click', function() {
                const hiddenItems = dilemmas_list.find('.accordion-item:hidden');
                const visibleItems = dilemmas_list.find('.accordion-item:visible');
                
                if (hiddenItems.length > 0) {
                  hiddenItems.removeClass('d-none');
                  $(this).text(_t('Show Less'));
                } else {
                  visibleItems.slice(15).addClass('d-none');
                  $(this).text(_t('Show More'));
                }
              });

              dilemma_priritise.removeClass('d-none');
              dilemma_loader.hide();
            }
          }
         
          if (data.data.agenda.length > 0) {
            clearInterval(pollingInterval); // Stop polling
            agendaSpinner.hide();
            data.data.agenda.forEach((agenda, index) => {
              
              // const listItem = `  <li class="list-group-item">
              //                                   <strong style="text-transform: capitalize">  ${agenda.title}</strong>
              //                           </li>`;

              const listItem = `  <div class="accordion-item">
                                            <h2 class="accordion-header" id="heading${index}">
                                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                                    data-bs-target="#collapse${index}" aria-expanded="false"
                                                    aria-controls="collapse${index}">
                                                    ${agenda.title}
                                                </button>
                                            </h2>
                                            <div id="collapse${index}" class="accordion-collapse collapse"
                                                aria-labelledby="heading${index}" data-bs-parent="#agendaAccordion">
                                                <div class="accordion-body">
                                                <ul>
                                                  <li><p class="fw-bold">${_t("Grouped Dilemmas for the respective Agenda:")}</p></li>
                                                  <ul>
                                                    ${agenda.dilemmas
                                                      .map(
                                                        (dilemma, idx) => {
                                                      
                                                          return `<li class="fw-bold"><a href="#dilemma-${dilemma.id}" class="text-decoration-none" >${dilemma.title}</a></li>`;
                                                        }
                                                      )
                                                      .join('')}
                                                  </ul>
                                                </ul>
                                                
                                                <p class="fw-bold"><i>${_t("Reference")}</i></p>
                                                <ul>
                                                ${agenda.reference.pdf && agenda.reference.pdf.length > 0 ? `
                                                  <li>
                                                    <strong>${_t("PDF")}</strong>
                                                    <ul>
                                                      ${agenda.reference.pdf.map((pdf) => `<li class="">${pdf}</li>`).join('')}
                                                    </ul>
                                                  </li>
                                                ` : ''}


                                                ${agenda.reference.printed_media && agenda.reference.printed_media.length > 0 ? `
                                                  <li>
                                                    <strong>${_t("Printed media")}</strong>
                                                    <ul>
                                                      ${agenda.reference.printed_media.map((printed_media) => `<li class="">
                                                      <a href="${printed_media}" target="_blank">${printed_media}</a></li>`).join('')}
                                                    </ul>
                                                  </li>
                                                ` : ''}

                                                ${agenda.reference.social_media && agenda.reference.social_media.length > 0 ? `
                                                  <li>
                                                    <strong>${_t("Social media")}</strong>
                                                    <ul>
                                                      ${agenda.reference.social_media.map((social_media) => `<li class=""><a href="${social_media}" target="_blank">${social_media}</a></li>`).join('')}
                                                    </ul>
                                                  </li>
                                                ` : ''}
                                                </ul>
                                                </div>
                                            </div>
                                        </div>`;
              agendas_list.append(listItem);
            });
            agenda_loader.hide();

            // Display n-gram visualization if available after agenda is loaded
            if (data.data.n_gram && data.data.n_gram.attachment) {
              const ngramImage = $('<img/>', {
                src: `data:image/png;base64,${data.data.n_gram.attachment}`,
                alt: _t('N-gram Visualization'),
                class: 'img-fluid mb-4',
                style: 'max-width: 100%; height: auto;'
              });
              ngramContainer.empty().append(ngramImage);
            } else {
              ngramLoader.hide();
            }
          }
        })
        .catch((error) => {
          console.error('❌ Error fetching results:', error);
        });
    }

    // Start polling every 5 seconds if status is "in_progress"
    let pollingInterval = setInterval(fetchResults, 5000);

    // Fetch immediately on page load
    fetchResults();

    $('#submit-prioritize').click(function () {
      const project_id = $('#project_id').val();

      let criteria1 = (parseFloat($('#criteria1').val()) || 0) * 10;
      let criteria2 = (parseFloat($('#criteria2').val()) || 0) * 10;
      let criteria3 = (parseFloat($('#criteria3').val()) || 0) * 10;
      let criteria4 = (parseFloat($('#criteria4').val()) || 0) * 10;

      let total = (criteria1 + criteria2 + criteria3 + criteria4) / 10;
      
      if (total !== 1) {
        alert(`The sum of all criteria weights must be exactly 1. Total: ${total}`);
        return;
      }

      let data = {
        env: criteria1 / 10,
        eco: criteria2 / 10,
        soc: criteria3 / 10,
        tech: criteria4 / 10,
      };

      axios
        .post(`/cep_agenda_generation/${project_id}/prioritize`, data)
        .then((response) => {
          location.reload();
        })
        .catch((error) => {
          alert(_t('An error occurred while submitting data.'));
          console.error(error);
        });
    });

    let frequencyPollingInterval = null
    const frequencyContainer = $('#frequency-container');
    const frequencyLoader = createLoader(_t('Generating frequency analysis...'));
    frequencyContainer.append(frequencyLoader);
    frequencyLoader.hide();
    $("#get-frequency-btn").prop('disabled', true);

    $('#get-frequency-btn').on('click', function() {
      const $btn = $(this);
      $btn.prop('disabled', true);
      frequencyLoader.show();
      const project_id = $('#project_id').val();

      axios.post(`/cep_agenda_generation/get_frequency`, {
        project_id: project_id
      })
      .then(response => {
        frequencyPollingInterval = setInterval(pollFrequencyData, 5000);
      })
      .catch(error => {
        console.error('Error:', error);
        $btn.prop('disabled', false);
        frequencyLoader.hide();
        frequencyContainer.append(`<p class="text-danger">${_t('Failed to start frequency analysis. Please try again.')}</p>`);
      });
    });

    function pollFrequencyData() {
      axios.get(`/cep_agenda_generation/${project_id}/frequency`)
        .then(response => {
          const data = response.data;
          
          if (data.status === "running") {
            return;
          }

          if (data.status === "idle" || data.status === "completed") {
            clearInterval(frequencyPollingInterval);
            frequencyLoader.hide();
          }

          if (data.data && data.data.length > 0) {
            const row = $('<div/>', {
              class: 'row g-4'
            });
            
            // Create three columns for the images
            data.data.map((image, i) => {
              const col = $('<div/>', {
                class: 'col-md-4'
              });
              
              const frequencyImage = $('<img/>', {
                src: `data:image/png;base64,${image.attachment}`,
                alt: `Frequency Analysis ${i + 1}`,
                class: 'img-fluid rounded shadow-sm',
                style: 'width: 100%; height: auto; object-fit: contain;'
              });
              
              col.append(frequencyImage);
              row.append(col);
            });

            frequencyContainer.empty().append(row);
            $("#get-frequency-btn").hide();
          } else {
           
            $("#get-frequency-btn").prop('disabled', false).show();
          }
        })
        .catch(error => {
          console.error('Error fetching frequency data:', error);
          clearInterval(frequencyPollingInterval);
          frequencyLoader.hide();
          frequencyContainer.empty().append(`<p class="text-danger">${_t('Error loading frequency analysis.')}</p>`);
          $("#get-frequency-btn").prop('disabled', false).show();
        });
    }
    frequencyPollingInterval = setInterval(pollFrequencyData, 5000);



  });
});

$(document).ready(function () {
  // Add click handler for dilemma links
  $(document).on('click', '.accordion-body a[href^="#dilemma-"]', function(e) {
    e.preventDefault();
    const targetId = $(this).attr('href');
    const targetElement = $(targetId);
 
 
    if (targetElement.length) {
      // Remove d-none class if present
      if (targetElement.hasClass('d-none')) {
       
        const showMoreBtn = $('#show-more-dilemmas');
        if (showMoreBtn.length) {
          showMoreBtn.trigger('click');
        }
      }

      const targetElem = document.querySelector(targetId);
      const scrollTarget = targetElem.previousElementSibling || document.querySelector('#output-header');
      if (scrollTarget) {
        scrollTarget.scrollIntoView({ behavior: "smooth" });
      }
      // $('main').animate({
      //   scrollTop: (targetElement.offset().top - 50)  // Subtract navbar height and add a small margin
      // }, 800);
      targetElement.addClass('highlight-dilemma');
      setTimeout(() => targetElement.removeClass('highlight-dilemma'), 2000);
    }
});
  // Add CSS for highlight effect
  $('<style>').text('.highlight-dilemma { background-color: #fff3cd; transition: background-color 0.5s; }').appendTo('head');
});
