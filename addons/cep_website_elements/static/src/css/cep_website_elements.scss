/* Custom Hero Section Styles */
.custom-hero {
  background-image: url('/cep_website_elements/static/src/img/hero_cover.png');
  background-size: cover;
  background-position: center;
  color: white;
  padding: 150px 0px;

  /* Hero Text Styles */
  .welcome-text {
    font-weight: 300;
    font-size: 32px;
    line-height: 100%;
    letter-spacing: 0%;
  }

  .citizentone-text {
    font-weight: 800;
    font-size: 70px;
    line-height: 72px;
    letter-spacing: 0%;
    color: #1FCCC6;
  }
}

/* Button Styles */
.join-platform {
  width: 213px;
  height: 56px;
  border-radius: 38px;
  border-width: 1px;
  border-color: #1FCCC6;
  color: #1FCCC6;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 52px;

  &:hover {
    background-color: #1FCCC6;
    color: white;
  }
}

/* Responsive Design for Medium Screens (Tablets) */
@media (min-width: 768px) and (max-width: 1024px) {
  .custom-hero {
    background-image: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)),
      url('/cep_website_elements/static/src/img/hero_cover.png') !important;
    background-position: center;
  }
}

/* Responsive Design for Small Screens (Mobile) */
@media (max-width: 767px) {
  .custom-hero {
    &.oe_img_bg.o_bg_img_center {
      background-position: left;
    }

    background-position: left;
    text-align: center;

    .citizentone-text {
      font-size: 40px;
      line-height: 48px;
    }
  }

  .join-platform {
    width: 180px;
    height: 50px;
    margin: 0 auto;
    margin-top: 40px;
  }
}