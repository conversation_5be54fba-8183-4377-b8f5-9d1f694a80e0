<?xml version="1.0"?>
<odoo>
    <!-- Search (Filter) View -->
    <record id="view_cep_ccf_project_search" model="ir.ui.view">
        <field name="name">cep.ccf.project.search</field>
        <field name="model">cep.ccf.project</field>
        <field name="arch" type="xml">
            <search string="CEP CCF Project">
                <field name="title" />
                <field name="description_public" />
                <field name="description_private" />
            </search>
        </field>
    </record>

    <!-- List (Tree) View -->
    <record id="view_cep_ccf_project_tree" model="ir.ui.view">
        <field name="name">cep.ccf.project.tree</field>
        <field name="model">cep.ccf.project</field>
        <field name="arch" type="xml">
            <tree>
                <field name="title" />
                <field name="start_date" />
                <field name="end_date" />
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_cep_ccf_project_form" model="ir.ui.view">
        <field name="name">cep.ccf.project.form</field>
        <field name="model">cep.ccf.project</field>
        <field name="arch" type="xml">
            <form string="Projects">
                <header>
                    <!-- Add a button box on top of the form view -->
                    <!-- <button string="Phases" class="oe_highlight" icon="fa-bolt" type="object"
                    name="action_open_project_phase_form_view"/>
                    <button string="Button 2" class="oe_highlight" icon="fa-check"/> -->
                </header>
                <sheet>
                    <h3>Project Initialization and Preparation</h3>
                    <hr />
                    <field name="cover_photo" widget="image" class="oe_avatar" />
                    <group>
                        <field name="title" />
                        <field name="description_public" />
                        <field name="description_private" />
                        <!-- <field name="tag_ids" widget="many2many_tags"/> -->
                        <field name="location" />
                        <field name="start_date" />
                        <field name="end_date" />
                    </group>
                    <notebook>
                        <page name="phases" string="Phases">
                            <field name="phase_ids" nolabel="1">
                                <form>
                                    <group>
                                        <field name="phase_name" widget="selection" />
                                        <field name="description" />
                                        <field name="currency_id" widget="selection" />
                                        <field name="budget" widget="monetary" />
                                        <field name="start_date" />
                                        <field name="end_date" />
                                        <field name="responsible_user_id" />
                                    </group>
                                </form>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- action -->
    <record id='cep_ccf_project_action' model='ir.actions.act_window'>
        <field name="name">Projects</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">cep.ccf.project</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new project
            </p>
        </field>
    </record>

    <!-- menu -->
    <menuitem id="cep_ccf_root" name="Co-creation Co-design Framework" sequence="0">
        <menuitem id="cep_ccf_project" name="Projects" action="cep_ccf_project_action" />
    </menuitem>
</odoo>