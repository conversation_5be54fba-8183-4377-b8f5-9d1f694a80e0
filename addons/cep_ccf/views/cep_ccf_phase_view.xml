<?xml version="1.0"?>
<odoo>
    <!-- Search (Filter) View -->
    <record id="view_cep_ccf_phase_search" model="ir.ui.view">
        <field name="name">cep.ccf.phase.search</field>
        <field name="model">cep.ccf.phase</field>
        <field name="arch" type="xml">
            <search string="CEP CCF Phases">
                <field name="phase_name" />
                <field name="description" />
            </search>
        </field>
    </record>

    <!-- List (Tree) View -->
    <record id="view_cep_ccf_phase_tree" model="ir.ui.view">
        <field name="name">cep.ccf.phase.tree</field>
        <field name="model">cep.ccf.phase</field>
        <field name="arch" type="xml">
            <tree>
                <field name="phase_name" />
                <field name="responsible_user_id" />
                <field name="start_date" />
                <field name="end_date" />
                <button string="Create Activities" class="oe_highlight" type="object"
                    name="action_open_activity_form_view" />
                <button string="Activity List" class="oe_highlight" type="object"
                    name="action_open_activity_tree_view" />
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_cep_ccf_phase_form" model="ir.ui.view">
        <field name="name">cep.ccf.phase.form</field>
        <field name="model">cep.ccf.phase</field>
        <field name="arch" type="xml">
            <form string="CCF Phase">
                <sheet>
                    <group>
                        <field name="phase_name" widget="selection" />
                        <field name="description" />
                        <field name="currency_id" widget="selection" />
                        <field name="budget" widget="monetary" />
                        <field name="start_date" />
                        <field name="end_date" />
                        <field name="project_id" />
                        <field name="responsible_user_id" />
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- action -->
    <record id='cep_ccf_phase_action' model='ir.actions.act_window'>
        <field name="name">CCF Phase</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">cep.ccf.phase</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new phase
            </p>
        </field>
    </record>

    <!-- menu -->
    <!-- <menuitem id="cep_ccf_phase" name="Phases" parent="cep_ccf_root"
    action="cep_ccf_phase_action"/> -->
</odoo>