<?xml version="1.0"?>
<odoo>
    <!-- Search (Filter) View -->
    <record id="view_cep_ccf_activity_search" model="ir.ui.view">
        <field name="name">cep.ccf.activity.search</field>
        <field name="model">cep.ccf.activity</field>
        <field name="arch" type="xml">
            <search string="Activities">
                <field name="activity_name" />
                <field name="description" />
            </search>
        </field>
    </record>

    <!-- List (Tree) View -->
    <record id="view_cep_ccf_activity_tree" model="ir.ui.view">
        <field name="name">cep.ccf.activity.tree</field>
        <field name="model">cep.ccf.activity</field>
        <field name="arch" type="xml">
            <tree>
                <field name="activity_name" />
                <field name="start_date" />
                <field name="end_date" />
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_cep_ccf_activity_form" model="ir.ui.view">
        <field name="name">cep.ccf.activity.form</field>
        <field name="model">cep.ccf.activity</field>
        <field name="arch" type="xml">
            <form string="CCF Activity">
                <sheet>
                    <group>
                        <field name="activity_name" />
                        <field name="description" />
                        <field name="start_date" />
                        <field name="end_date" />
                        <field name="activity_steps" widget="selection" />
                        <field name="phase_id" widget="selection"
                            options="{'no_create': True, 'no_open': True}" readonly="1"
                            invisible="1" />
                        <field name="phase_name" widget="selection"
                            options="{'no_create': True, 'no_open': True}" readonly="1" />
                        <field name="status" />
                        <field name="is_milestone" />
                        <field name="assign_ids" widget="many2many_tags" />
                    </group>
                    <notebook>
                        <page name="deliverables" string="Deliverables">
                            <field name="deliverable_ids" nolabel="1">
                                <form>
                                    <group>
                                        <field name="deliverable_name" />
                                        <field name="deliverable_description" />
                                        <field name="end_date" />
                                    </group>
                                </form>
                            </field>
                        </page>
                        <page name="data" string="Data Collection">
                            <field name="activity_data_ids" nolabel="1">
                                <form>
                                    <group>
                                        <field name="criteria" />
                                        <field name="data" />
                                        <field name="data_source" />
                                        <field name="data_overview" />
                                        <field name="data_availability" />
                                        <field name="region" />
                                    </group>
                                </form>
                            </field>
                        </page>
                        <page name="survey" string="Survey">
                            <field name="survey_ids" nolabel="1">
                                <form>
                                    <group>
                                        <field name="title" />
                                        <field name="survey_link" />
                                    </group>
                                </form>
                            </field>
                        </page>
                        <page name="focus_group" string="Focus Group">
                            <field name="focus_group_ids" nolabel="1">
                                <form>
                                    <group>
                                        <field name="focus_group_name" />
                                        <field name="participant_info" />
                                        <field name="recording_format" />
                                        <field name="recording_time" />
                                        <field name="transcription_notes" />
                                    </group>
                                </form>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Calender View -->
    <record id="view_cep_ccf_activity_calendar" model="ir.ui.view">
        <field name="name">cep.ccf.activity.calendar</field>
        <field name="model">cep.ccf.activity</field>
        <field name="arch" type="xml">
            <calendar string="CEP CCF Activity" date_start="start_date">
                <field name="activity_name" />
                <!-- <field name="phase_id"/>
                <field name="criteria_id"/>
                <field name="start_date"/>
                <field name="end_date"/> -->
            </calendar>
        </field>
    </record>

    <!-- timeline view -->
    <record id="view_cep_ccf_activity_timeline" model="ir.ui.view">
        <field name="name">cep.ccf.activity.timeline</field>
        <field name="model">cep.ccf.activity</field>
        <field name="arch" type="xml">
            <timeline date_start="start_date" date_stop="end_date" default_group_by="project_id" />
        </field>
    </record>

    <!-- action -->
    <record id='cep_ccf_activity_action' model='ir.actions.act_window'>
        <field name="name">CCF Activity</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">cep.ccf.activity</field>
        <field name="view_mode">tree,form,calendar,timeline</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new activity
            </p>
        </field>
    </record>

    <!-- menu -->
    <menuitem id="cep_ccf_activity" name="Activities" parent="cep_ccf_root"
        action="cep_ccf_activity_action" />
</odoo>