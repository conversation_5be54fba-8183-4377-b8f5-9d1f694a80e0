from odoo import models, fields, api
from datetime import datetime


class Phase(models.Model):
    _name = 'cep.ccf.phase'
    _description = 'CEP CCF Phase'
    _rec_name = 'phase_name'

    PHASE_SELECTION = [
        ('phase1', 'Know'),
        ('phase2', 'Raise Awareness'),
        ('phase3', 'Co-creation'),
        ('phase4', 'Co-design'),
        ('phase5', 'Keep Engaged'),
        ('phase6', 'Outreach'),
    ]
    ALLOWED_CURRENCY_NAME = ['USD', 'GBP']

    # pre defined phase name
    phase_name = fields.Selection(
        selection=PHASE_SELECTION, string='Phase Name', required=True)
    description = fields.Text('Phase Description', required=True)
    currency_id = fields.Many2one(
        'res.currency',
        string='Currency',
        default=lambda self: self.env['res.currency'].search(
            [('name', '=', 'GBP'),], limit=1),
        domain=[('name', 'in', ALLOWED_CURRENCY_NAME)],
        required=True
    )
    budget = fields.Monetary(string='Budget', currency_field='currency_id')
    start_date = fields.Date(string='Start Date', default=lambda self: (
        datetime.now()).strftime('%Y-%m-%d'), required=True)
    end_date = fields.Date(string='End Date', default=lambda self: (
        datetime.now()).strftime('%Y-%m-%d'), required=True)
    project_id = fields.Many2one('cep.ccf.project', required=True)
    owner_id = fields.Many2one('res.users', string='Owner', index=True,
                               tracking=True, default=lambda self: self.env.user)
    responsible_user_id = fields.Many2one(
        'res.users', string='Responsible Person', index=True, tracking=True, required=True)
    activity_ids = fields.One2many(
        'cep.ccf.activity', 'phase_id', string='Activities')

    # get user list by the company of user loggeed in
    @api.model
    def _get_user_list(self):
        return self.env['res.users'].search([('company_id', '=', self.env.user.company_id.id)])

    @api.onchange('responsible_user_id')
    def _onchange_responsible_user_id(self):
        if self.responsible_user_id:
            self.send_email_to_responsible_user()

    def send_email_to_responsible_user(self):
        # Compose the email content
        subject = f'Assigned as the Responsible Person for Phase {dict(self.PHASE_SELECTION).get(self.phase_name, self.phase_name)} in the Project {self.project_id.title}'
        body = f'Dear {self.responsible_user_id.name},<br><br>' \
            f'You have been assigned as the Responsible Person for Phase {dict(self.PHASE_SELECTION).get(self.phase_name, self.phase_name)} in the Project {self.project_id.title}.<br><br>' \
            f'Please take the necessary actions.<br><br>' \
            f'Best regards,<br>The TVS Team'

        # Send the email
        self.env['mail.mail'].create({
            'subject': subject,
            'body_html': body,
            'email_to': self.responsible_user_id.login,
        }).send()

    def action_open_activity_form_view(self):
        # Assuming there's a one-to-many field named 'activity_ids' on 'cep.ccf.phase'
        activity = self.env['cep.ccf.activity'].new({
            # 'phase_id': self.id,
            # Add other required fields for your activity
        })

        view_id = self.env.ref('cep_ccf.view_cep_ccf_activity_form').id
        return {
            'name': 'Add Activity',
            'view_mode': 'form',
            'view_id': view_id,
            'res_model': 'cep.ccf.activity',
            # 'res_id': activity.id,
            'type': 'ir.actions.act_window',
            'target': 'current',
            'context': {
                'default_phase_id': self.id,  # Set the default value for phase_id
            },
        }

    def action_open_activity_tree_view(self):

        # view_id = self.env.ref('cep_ccf.view_cep_ccf_activity_tree').id
        create_view_id = self.env.ref('cep_ccf.view_cep_ccf_activity_form').id
        tree_view_id = self.env.ref('cep_ccf.view_cep_ccf_activity_tree').id
        return {
            'name': 'Activity List',
            'view_mode': 'tree',
            'views': [(tree_view_id, 'tree'), (create_view_id, 'form')],
            'res_model': 'cep.ccf.activity',
            'type': 'ir.actions.act_window',
            'target': 'current',
            'domain': [('phase_id', '=', self.id)],
            'context': {
                'default_phase_id': self.id,  # Set the default value for phase_id
            },
        }
