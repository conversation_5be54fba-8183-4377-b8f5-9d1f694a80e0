<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="cep_idea.ideas_view" name="Idea">
        <t t-call="website.layout">
            <t t-if=" idea != undefined ">
                <t t-set="title">Idea Details</t>
                <t t-set="head">
                    <t t-call-assets="web.assets_common" />
                    <t t-call-assets="web.assets_frontend" />
                    <t t-call-assets="cep_idea.cep_idea_common" />
                    <t t-call-assets="cep_idea.idea_details" />
                    <t t-call-assets="cep_idea.cep_idea_comment" />
                </t>
                <div class="oe_structure">
                    <section class="s_idea_details pt-3 bg-white">
                        <div class="container">
                            <div class="row my-4">
                                <div class="col-12">
                                    <div
                                        class="d-flex flex-row align-items-center gap-2 mb-2 problem-title"
                                        t-attf-data-href="/problems/#{str(problem.id)}/view">
                                        <i class="fa fa-angle-left rounded-circle"
                                            aria-hidden="true"></i>
                                        <h5 class="fw-500 c-dark-gunmetal text"><t
                                                t-esc="problem.title" /> - <t
                                                t-esc="problem.description[:48]" />...</h5>
                                    </div>
                                </div>

                                <div class="col-12 col-md-8">
                                    <div class="d-flex flex-column gap-4 mb-3">
                                        <!-- cover image -->
                                        <t t-if="idea.cover_photo">
                                            <div class="featured-image"
                                                t-attf-style="background-image: url('{{ image_data_uri(idea.cover_photo) }}');"
                                                alt="idea cover"></div>
                                        </t>
                                        <t t-else="">
                                            <div class="featured-image"
                                                t-attf-style="background-image: url('/cep_idea/static/src/img/placeholder.png');"
                                                alt="idea cover"></div>
                                        </t>
                                        <!-- author name and location -->
                                        <div class="d-flex flex-row align-items-center gap-2">
                                            <svg width="80" height="80" viewBox="0 0 50 50"
                                                fill="none"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <rect width="50" height="50" rx="25" fill="#F2F2F2" />
                                                <path
                                                    d="M33.6668 34.75V32.5833C33.6668 31.4341 33.2103 30.3319 32.3976 29.5192C31.585 28.7065 30.4828 28.25 29.3335 28.25H20.6668C19.5176 28.25 18.4154 28.7065 17.6027 29.5192C16.79 30.3319 16.3335 31.4341 16.3335 32.5833V34.75"
                                                    stroke="#1A2942" stroke-width="2"
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round" />
                                                <path
                                                    d="M24.9998 23.9167C27.3931 23.9167 29.3332 21.9766 29.3332 19.5833C29.3332 17.1901 27.3931 15.25 24.9998 15.25C22.6066 15.25 20.6665 17.1901 20.6665 19.5833C20.6665 21.9766 22.6066 23.9167 24.9998 23.9167Z"
                                                    stroke="#1A2942" stroke-width="2"
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round" />
                                            </svg>
                                            <div class="d-flex flex-column">
                                                <div
                                                    class="d-flex flex-column flex-md-row align-items-md-center gap-2">
                                                    <span class="author">
                                                        <t t-esc="idea.owner_id.name" />
                                                    </span>
                                                    <span class="date">- <t
                                                            t-esc="idea.format_create_date()" /></span>
                                                </div>
                                                <div
                                                    class="d-flex flex-row align-items-center gap-2">
                                                    <i class="fa fa-map participants"></i>
                                                    <span class="location">
                                                        <t t-esc="problem.location" />
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- idea title -->
                                        <h3 class="fw-700 c-dark-gunmetal">
                                            <t t-esc="idea.title" />
                                        </h3>
                                        <!-- idea description -->
                                        <p class="fw-500 text-justify">
                                            <t t-esc="idea.description" />
                                        </p>
                                        <!-- <a href="#">Read More</a> -->
                                    </div>
                                    <t t-if="idea.attachment_ids">
                                        <div class='mb-4'>
                                            <h6 class='my-0'>Download Attachments</h6>

                                            <t t-foreach="idea.attachment_ids" t-as="attachment">
                                                <a
                                                    t-att-href="'/web/content/' + str(attachment.id) + '?download=true'"
                                                    class="d-block mt-2 ">
                                                    <i class="fa fa-download"></i> Download <t
                                                        t-esc="attachment.name" />
                                                </a>
                                            </t>
                                        </div>
                                    </t>

                                    <!-- official update section start -->
                                    <t t-if="idea.last_official_update()">
                                        <t t-set="official_update"
                                            t-value="idea.last_official_update()" />
                                        <div class="d-flex flex-column official-update">
                                            <h4 class="fw-700 c-red">Official Update</h4>

                                            <div class="alert alert-seashell p-5" role="alert">
                                                <p>
                                                    <t t-esc="official_update.message" />
                                                </p>
                                                <p class="mb-0 date">Posted on - <t
                                                        t-esc="official_update.format_create_date()" /></p>
                                            </div>

                                        </div>
                                    </t>


                                    <t t-if="isIdeaOwner">
                                        <div class="d-flex flex-column official-update">
                                            <h4 class="fw-700 text-danger">Add an Official Update</h4>

                                            <form class="d-flex flex-column gap-3"
                                                t-att-action="'official-update/create'"
                                                method="POST">
                                                <input type="hidden" name="csrf_token"
                                                    t-att-value="request.csrf_token()" />
                                                <div class="form-floating">
                                                    <textarea rows="6" class="form-control"
                                                        placeholder="Message"
                                                        id="official-update-message" name="message"></textarea>
                                                    <label for="official-update-message">Message</label>
                                                </div>

                                                <div class="d-flex flex-row gap-2">
                                                    <button type="submit" class="btn btn-post">
                                                        Publish</button>
                                                </div>

                                            </form>
                                        </div>
                                    </t>

                                    <!-- comments section start -->
                                    <div class="d-flex flex-column my-4">
                                        <div class="d-flex justify-content-between gap-3">
                                            <h3 class="fw-700 c-dark-gunmetal">Comments (<t
                                                    t-esc="comment_count" />)</h3>
                                            <div class="dropdown">
                                                <button
                                                    class="btn btn-md btn-filter dropdown-toggle"
                                                    type="button"
                                                    id="dropdownMenuButton1"
                                                    data-bs-toggle="dropdown"
                                                    aria-expanded="false">
                                                    <t t-out="filterType" />
                                                </button>
                                                <ul class="dropdown-menu"
                                                    aria-labelledby="dropdownMenuButton1">
                                                    <li>
                                                        <a class="dropdown-item"
                                                            href="?filter=newest">Newest</a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item"
                                                            href="?filter=oldest">Oldest</a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="?filter=all">
                                                            All</a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Main Comment Form -->
                                    <div class="d-flex flex-row my-4">
                                        <svg class="flex-shrink-0" width="50" height="50"
                                            viewBox="0 0 50 50"
                                            fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <rect width="50" height="50" rx="25" fill="#EEF3FE" />
                                            <path
                                                d="M33 34V32C33 30.9391 32.5786 29.9217 31.8284 29.1716C31.0783 28.4214 30.0609 28 29 28H21C19.9391 28 18.9217 28.4214 18.1716 29.1716C17.4214 29.9217 17 30.9391 17 32V34"
                                                stroke="#1A2942" stroke-width="2"
                                                stroke-linecap="round"
                                                stroke-linejoin="round" />
                                            <path
                                                d="M25 24C27.2091 24 29 22.2091 29 20C29 17.7909 27.2091 16 25 16C22.7909 16 21 17.7909 21 20C21 22.2091 22.7909 24 25 24Z"
                                                stroke="#1A2942" stroke-width="2"
                                                stroke-linecap="round"
                                                stroke-linejoin="round" />
                                        </svg>

                                        <form class="d-flex flex-column flex-grow-1 ms-2 gap-2"
                                            t-att-action="'comments/' + 'None' + '/create'"
                                            method="POST">
                                            <div class="form-floating">
                                                <input type="hidden" name="csrf_token"
                                                    t-att-value="request.csrf_token()" />
                                                <textarea name="message" t-att-value="message"
                                                    id="comment" rows="6"
                                                    class="form-control"
                                                    placeholder="Write your comment here ..."
                                                    required="required"></textarea>
                                                <label for="comment">Write your comment here ...</label>
                                            </div>

                                            <div class="d-flex flex-row gap-2">
                                                <button type="submit" class="btn btn-post">Post</button>
                                            </div>

                                        </form>
                                    </div>

                                    <div id="comments" class="d-flex flex-column gap-3">
                                        <!-- Comments -->
                                        <t t-foreach="idea.get_parent_comments(filterType)"
                                            t-as="comment">
                                            <div class="d-flex flex-row mb-2"
                                                t-attf-id="comment-#{comment.id}">
                                                <svg class="flex-shrink-0" width="50" height="50"
                                                    viewBox="0 0 50 50"
                                                    fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <rect width="50" height="50" rx="25"
                                                        fill="#EEF3FE" />
                                                    <path
                                                        d="M33 34V32C33 30.9391 32.5786 29.9217 31.8284 29.1716C31.0783 28.4214 30.0609 28 29 28H21C19.9391 28 18.9217 28.4214 18.1716 29.1716C17.4214 29.9217 17 30.9391 17 32V34"
                                                        stroke="#1A2942" stroke-width="2"
                                                        stroke-linecap="round"
                                                        stroke-linejoin="round" />
                                                    <path
                                                        d="M25 24C27.2091 24 29 22.2091 29 20C29 17.7909 27.2091 16 25 16C22.7909 16 21 17.7909 21 20C21 22.2091 22.7909 24 25 24Z"
                                                        stroke="#1A2942" stroke-width="2"
                                                        stroke-linecap="round"
                                                        stroke-linejoin="round" />
                                                </svg>

                                                <div
                                                    class="d-flex flex-column flex-grow-1 ms-2 gap-2">
                                                    <div
                                                        class="d-flex flex-row justify-content-between">
                                                        <div
                                                            class="d-flex flex-column flex-md-row gap-2">
                                                            <span class="author">
                                                                <t t-esc="comment.owner_id.name" />
                                                            </span>
                                                            <span class="date">
                                                                <t
                                                                    t-esc="comment.create_date.strftime('%d %b %Y')" />
                                                            </span>
                                                        </div>
                                                        <t t-if="uid == comment.owner_id.id">
                                                            <button t-att-data-id="comment.id"
                                                                t-att-data-type="'comment'"
                                                                class="btn delete">
                                                                <i class="fa fa-trash-o"
                                                                    aria-hidden="true"></i> <!--
                                                                Delete icon -->
                                                            </button>
                                                        </t>
                                                    </div>
                                                    <p class="text-justify">
                                                        <t t-esc="comment.message" />
                                                    </p>

                                                    <div
                                                        class="reaction-on-comment d-flex flex-row gap-4">
                                                        <button
                                                            class="btn reaction_button d-flex flex-row align-items-center text-decoration-none gap-2"
                                                            t-att-data-model="'ideas_comments'"
                                                            t-att-data-id="comment.id"
                                                            t-att-data-type="'like'">
                                                            <i
                                                                class="fa fa-thumbs-o-up rounded-circle"
                                                                aria-hidden="true"></i>
                                                            <span>Like(<span
                                                                    t-attf-id="likes_count_ideas_comments_#{comment.id}"
                                                                    t-esc="comment.count_like()" />)</span>
                                                        </button>

                                                        <button
                                                            class="btn reaction_button d-flex flex-row align-items-center text-decoration-none gap-2"
                                                            t-att-data-model="'ideas_comments'"
                                                            t-att-data-id="comment.id"
                                                            t-att-data-type="'dislike'">
                                                            <i
                                                                class="fa fa-thumbs-o-down rounded-circle"
                                                                aria-hidden="true"></i>
                                                            <span>Dislike(<span
                                                                    t-attf-id="dislikes_count_ideas_comments_#{comment.id}"
                                                                    t-esc="comment.count_dislike()" />
                                                                )</span>
                                                        </button>
                                                    </div>

                                                    <!-- Replies -->
                                                    <t
                                                        t-foreach="idea.get_child_comments(comment.id)"
                                                        t-as="reply">
                                                        <div class="d-flex flex-row mb-2"
                                                            t-attf-id="reply-#{reply.id}">
                                                            <svg class="flex-shrink-0" width="50"
                                                                height="50"
                                                                viewBox="0 0 50 50" fill="none"
                                                                xmlns="http://www.w3.org/2000/svg">
                                                                <rect width="50" height="50" rx="25"
                                                                    fill="#EEF3FE" />
                                                                <path
                                                                    d="M33 34V32C33 30.9391 32.5786 29.9217 31.8284 29.1716C31.0783 28.4214 30.0609 28 29 28H21C19.9391 28 18.9217 28.4214 18.1716 29.1716C17.4214 29.9217 17 30.9391 17 32V34"
                                                                    stroke="#1A2942"
                                                                    stroke-width="2"
                                                                    stroke-linecap="round"
                                                                    stroke-linejoin="round" />
                                                                <path
                                                                    d="M25 24C27.2091 24 29 22.2091 29 20C29 17.7909 27.2091 16 25 16C22.7909 16 21 17.7909 21 20C21 22.2091 22.7909 24 25 24Z"
                                                                    stroke="#1A2942"
                                                                    stroke-width="2"
                                                                    stroke-linecap="round"
                                                                    stroke-linejoin="round" />
                                                            </svg>

                                                            <div
                                                                class="d-flex flex-column flex-grow-1 ms-2 gap-2">
                                                                <div
                                                                    class="d-flex flex-row justify-content-between">
                                                                    <div
                                                                        class="d-flex flex-column flex-md-row gap-2">
                                                                        <span class="author">
                                                                            <t
                                                                                t-esc="reply.owner_id.name" />
                                                                        </span>
                                                                        <span class="date">
                                                                            <t
                                                                                t-esc="reply.create_date.strftime('%d %b %Y')" />
                                                                        </span>
                                                                    </div>
                                                                    <t
                                                                        t-if="uid == reply.owner_id.id">
                                                                        <button
                                                                            t-att-data-id="reply.id"
                                                                            t-att-data-type="'reply'"
                                                                            class="btn delete">
                                                                            <i class="fa fa-trash-o"
                                                                                aria-hidden="true"></i> <!--
                                                                            Delete icon -->
                                                                        </button>
                                                                    </t>
                                                                </div>
                                                                <p class="text-justify">
                                                                    <t t-esc="reply.message" />
                                                                </p>

                                                                <div
                                                                    class="reaction-on-comment d-flex flex-row gap-4">
                                                                    <button
                                                                        class="btn reaction_button d-flex flex-row align-items-center text-decoration-none gap-2"
                                                                        t-att-data-model="'ideas_comments'"
                                                                        t-att-data-id="reply.id"
                                                                        t-att-data-type="'like'">
                                                                        <i
                                                                            class="fa fa-thumbs-o-up rounded-circle"
                                                                            aria-hidden="true"></i>
                                                                        <span>Like(<span
                                                                                t-attf-id="likes_count_ideas_comments_#{reply.id}"
                                                                                t-esc="reply.count_like()" />
                                                                            )</span>
                                                                    </button>
                                                                    <button
                                                                        class="btn reaction_button d-flex flex-row align-items-center text-decoration-none gap-2"
                                                                        t-att-data-model="'ideas_comments'"
                                                                        t-att-data-id="reply.id"
                                                                        t-att-data-type="'dislike'">
                                                                        <i
                                                                            class="fa fa-thumbs-o-down rounded-circle"
                                                                            aria-hidden="true"></i>
                                                                        <span>Dislike(<span
                                                                                t-attf-id="dislikes_count_ideas_comments_#{reply.id}"
                                                                                t-esc="reply.count_dislike()" />
                                                                            )</span>
                                                                    </button>
                                                                </div>

                                                            </div>

                                                        </div>
                                                    </t>
                                                    <!-- Reply Form -->
                                                    <div class="d-flex flex-row mb-2">
                                                        <svg class="flex-shrink-0" width="50"
                                                            height="50"
                                                            viewBox="0 0 50 50" fill="none"
                                                            xmlns="http://www.w3.org/2000/svg">
                                                            <rect width="50" height="50" rx="25"
                                                                fill="#EEF3FE" />
                                                            <path
                                                                d="M33 34V32C33 30.9391 32.5786 29.9217 31.8284 29.1716C31.0783 28.4214 30.0609 28 29 28H21C19.9391 28 18.9217 28.4214 18.1716 29.1716C17.4214 29.9217 17 30.9391 17 32V34"
                                                                stroke="#1A2942" stroke-width="2"
                                                                stroke-linecap="round"
                                                                stroke-linejoin="round" />
                                                            <path
                                                                d="M25 24C27.2091 24 29 22.2091 29 20C29 17.7909 27.2091 16 25 16C22.7909 16 21 17.7909 21 20C21 22.2091 22.7909 24 25 24Z"
                                                                stroke="#1A2942" stroke-width="2"
                                                                stroke-linecap="round"
                                                                stroke-linejoin="round" />
                                                        </svg>

                                                        <form
                                                            class="d-flex flex-column flex-grow-1 ms-2 gap-2"
                                                            t-att-action="'comments/' + str(comment.id) + '/create'"
                                                            method="POST">
                                                            <input type="hidden" name="csrf_token"
                                                                t-att-value="request.csrf_token()" />
                                                            <div class="form-floating">
                                                                <textarea id="comment"
                                                                    class="form-control"
                                                                    name="message"
                                                                    placeholder="Write your reply here ..."
                                                                    rows="4"></textarea>
                                                                <label for="comment">Write your
                                                                    reply here ...</label>
                                                            </div>

                                                            <div class="d-flex flex-row gap-2">
                                                                <!-- <button type="submit"
                                                                class="btn
                                                                btn-cancel">Cancel</button> -->
                                                                <button type="submit"
                                                                    class="btn btn-post">Post</button>
                                                            </div>

                                                        </form>
                                                    </div>
                                                </div>

                                            </div>
                                        </t>
                                    </div>
                                </div>

                                <div class="col-12 col-md-4">
                                    <div class="card reaction-on-idea">
                                        <div class="card-body">
                                            <div class="d-flex flex-row gap-3">
                                                <button t-att-data-model="'ideas'"
                                                    t-att-data-id="idea.id"
                                                    t-att-data-type="'like'"
                                                    class="btn reaction_button d-flex flex-row align-items-center gap-2 text-decoration-none">
                                                    <i class="fa fa-thumbs-up rounded-circle"
                                                        aria-hidden="true"></i>
                                                    <span>
                                                        <span
                                                            t-attf-id="likes_count_ideas_#{idea.id}"
                                                            t-esc="idea.count_like()" />
                                                    </span>
                                                </button>

                                                <a t-att-data-model="'ideas'"
                                                    t-att-data-id="idea.id"
                                                    t-att-data-type="'dislike'"
                                                    class="btn reaction_button d-flex flex-row align-items-center gap-2 text-decoration-none">
                                                    <i class="fa fa-thumbs-down rounded-circle"
                                                        aria-hidden="true"></i>

                                                    <span>
                                                        <span
                                                            t-attf-id="dislikes_count_ideas_#{idea.id}"
                                                            t-esc="idea.count_dislike()" />
                                                    </span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="card mt-4">
                                        <div class="card-body">
                                            <div class="d-flex align-items-start flex-column">
                                                <h4 class="fw-700 c-maximum-blue-green">Current
                                                    status</h4>
                                                <span
                                                    class="badge badge-status py-1 c-dark-gunmetal opacity-100">
                                                    <t t-esc="idea.status.capitalize()" />
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="card mt-4">
                                        <div class="card-body">
                                            <div class="d-flex align-items-start flex-column">
                                                <h4 class="fw-700 c-maximum-blue-green">Tags</h4>
                                                <div
                                                    class="d-flex align-items-start flex-row flex-wrap gap-2">
                                                    <t t-foreach="idea.tag_ids" t-as="tag">
                                                        <span
                                                            class="badge badge-tag py-1 c-dark-gunmetal opacity-100">
                                                            <t t-esc="tag.name" />
                                                        </span>
                                                    </t>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="card mt-4">
                                        <div class="card-body">
                                            <div class="d-flex align-items-start flex-column">
                                                <h4 class="fw-700 c-maximum-blue-green">Share</h4>
                                                <div
                                                    class="d-flex align-items-start flex-row flex-wrap gap-2">
                                                    <a href="#" class="text-decoration-none">
                                                        <svg xmlns="http://www.w3.org/2000/svg"
                                                            width="44" height="44"
                                                            viewBox="0 0 44 44" fill="none">
                                                            <rect width="44" height="44" rx="22"
                                                                fill="#3B5998" />
                                                            <path
                                                                d="M26.3051 16.6701H24.7986C23.6178 16.6701 23.3898 17.2313 23.3898 18.0536V19.8629H26.2012V22.706H23.391V30H20.4541V22.706H18V19.8629H20.4541V17.7673C20.4035 17.27 20.4625 16.7677 20.6269 16.2957C20.7913 15.8237 21.0571 15.3935 21.4057 15.0352C21.7542 14.677 22.1771 14.3995 22.6444 14.2223C23.1117 14.0451 23.6122 13.9724 24.1107 14.0094H26.3038V16.6701H26.3051Z"
                                                                fill="white" />
                                                        </svg>
                                                    </a>

                                                    <a href="#" class="text-decoration-none">
                                                        <svg xmlns="http://www.w3.org/2000/svg"
                                                            width="44" height="44"
                                                            viewBox="0 0 44 44" fill="none">
                                                            <rect width="44" height="44" rx="22"
                                                                fill="#55ACEE" />
                                                            <path
                                                                d="M29.9842 16.5494C29.9732 16.5361 29.9581 16.5268 29.9413 16.5229C29.9244 16.519 29.9068 16.5207 29.891 16.5277C29.4116 16.7401 28.908 16.8927 28.3913 16.9821C28.9423 16.5686 29.3512 15.9941 29.5617 15.3382C29.567 15.3228 29.5674 15.3061 29.5629 15.2905C29.5583 15.2749 29.5491 15.261 29.5364 15.2508C29.5237 15.2406 29.5081 15.2346 29.4919 15.2336C29.4756 15.2325 29.4595 15.2365 29.4456 15.245C28.8277 15.6102 28.1529 15.869 27.4494 16.0108C26.9484 15.495 26.2941 15.155 25.584 15.0417C24.8739 14.9283 24.1462 15.0477 23.5096 15.382C22.8729 15.7162 22.3615 16.2474 22.0516 16.8963C21.7417 17.5452 21.65 18.2768 21.7902 18.9821C20.5269 18.9032 19.2936 18.564 18.1677 17.9856C17.0418 17.4073 16.0477 16.6024 15.2477 15.6215C15.2397 15.6114 15.2294 15.6035 15.2176 15.5983C15.2058 15.5932 15.1929 15.5911 15.1801 15.5922C15.1672 15.5932 15.1548 15.5973 15.1439 15.6042C15.133 15.6111 15.1239 15.6206 15.1175 15.6318C14.7171 16.3223 14.5777 17.1337 14.7248 17.9183C14.8719 18.703 15.2957 19.4088 15.9191 19.9074C15.5113 19.8585 15.1169 19.7311 14.7576 19.5322C14.7456 19.5253 14.7321 19.5216 14.7183 19.5216C14.7044 19.5216 14.6909 19.5251 14.6789 19.532C14.6669 19.5388 14.6568 19.5487 14.6498 19.5606C14.6428 19.5725 14.6391 19.586 14.6389 19.5999V19.642C14.6403 20.3521 14.868 21.0433 15.2889 21.6153C15.7098 22.1872 16.3021 22.6101 16.9797 22.8226C16.6127 22.8814 16.2382 22.8758 15.8731 22.806C15.8596 22.8033 15.8456 22.8042 15.8325 22.8086C15.8194 22.813 15.8077 22.8208 15.7986 22.8311C15.7894 22.8414 15.7831 22.8539 15.7803 22.8674C15.7775 22.881 15.7782 22.895 15.7825 22.9081C15.9837 23.5334 16.3654 24.0852 16.8796 24.4941C17.3938 24.9029 18.0175 25.1504 18.6721 25.2055C17.5666 26.0181 16.2292 26.4539 14.8572 26.4486C14.6019 26.4528 14.3466 26.4422 14.0926 26.4167C14.0749 26.4134 14.0566 26.4161 14.0407 26.4245C14.0247 26.4328 14.012 26.4462 14.0046 26.4627C13.9986 26.4795 13.9985 26.4979 14.0043 26.5148C14.0101 26.5317 14.0214 26.5462 14.0365 26.5558C15.5356 27.5184 17.2798 28.0297 19.0614 28.0287C20.2895 28.0408 21.5076 27.8079 22.6446 27.3435C23.7816 26.8791 24.8145 26.1926 25.6829 25.3241C26.5514 24.4557 27.2379 23.4228 27.7023 22.2858C28.1667 21.1489 28.3996 19.9307 28.3875 18.7026C28.3875 18.575 28.3849 18.4473 28.3798 18.3197C29.0094 17.8593 29.553 17.2916 29.9855 16.6426C29.9951 16.6289 30.0002 16.6126 30 16.5958C29.9998 16.5791 29.9942 16.5628 29.9842 16.5494Z"
                                                                fill="white" />
                                                        </svg>
                                                    </a>

                                                    <a href="#" class="text-decoration-none">
                                                        <svg width="44" height="44"
                                                            viewBox="0 0 44 44" fill="none"
                                                            xmlns="http://www.w3.org/2000/svg">
                                                            <rect width="44" height="44" rx="22"
                                                                fill="#0A66C2" />
                                                            <g clip-path="url(#clip0_0_1)">
                                                                <path
                                                                    d="M29.9968 30V29.9994H30.0008V24.1314C30.0008 21.2607 29.3828 19.0494 26.0268 19.0494C24.4135 19.0494 23.3308 19.9347 22.8888 20.774H22.8422V19.3174H19.6602V29.9994H22.9735V24.71C22.9735 23.3174 23.2375 21.9707 24.9622 21.9707C26.6615 21.9707 26.6868 23.56 26.6868 24.7994V30H29.9968Z"
                                                                    fill="white" />
                                                                <path
                                                                    d="M14.2637 19.3179H17.581V29.9999H14.2637V19.3179Z"
                                                                    fill="white" />
                                                                <path
                                                                    d="M15.9213 14C14.8607 14 14 14.8607 14 15.9213C14 16.982 14.8607 17.8607 15.9213 17.8607C16.982 17.8607 17.8427 16.982 17.8427 15.9213C17.842 14.8607 16.9813 14 15.9213 14V14Z"
                                                                    fill="white" />
                                                            </g>
                                                            <defs>
                                                                <clipPath id="clip0_0_1">
                                                                    <rect width="16" height="16"
                                                                        fill="white"
                                                                        transform="translate(14 14)" />
                                                                </clipPath>
                                                            </defs>
                                                        </svg>
                                                    </a>

                                                    <a href="#" class="text-decoration-none">
                                                        <svg xmlns="http://www.w3.org/2000/svg"
                                                            width="44" height="44"
                                                            viewBox="0 0 44 44" fill="none">
                                                            <g clip-path="url(#clip0_979_268)">
                                                                <path
                                                                    d="M22 44C34.1503 44 44 34.1503 44 22C44 9.84974 34.1503 0 22 0C9.84974 0 0 9.84974 0 22C0 34.1503 9.84974 44 22 44Z"
                                                                    fill="url(#paint0_linear_979_268)" />
                                                                <path fill-rule="evenodd"
                                                                    clip-rule="evenodd"
                                                                    d="M32.5825 11.688C29.9477 9.05095 26.4434 7.59797 22.7099 7.59634C15.0174 7.59634 8.75666 13.8548 8.75373 21.5473C8.75275 24.0061 9.39547 26.4065 10.6167 28.5221L8.63672 35.7518L16.0352 33.8119C18.0736 34.9233 20.3687 35.509 22.7047 35.5099H22.7105C22.7102 35.5099 22.7109 35.5099 22.7105 35.5099C30.4021 35.5099 36.6634 29.2509 36.6667 21.5584C36.6677 17.8304 35.2176 14.3251 32.5825 11.688ZM22.7102 33.1535H22.7053C20.6239 33.1525 18.5823 32.5939 16.8015 31.5372L16.3778 31.2859L11.9876 32.4371L13.1593 28.158L12.8835 27.7193C11.7223 25.873 11.1092 23.7391 11.1102 21.5479C11.1128 15.1542 16.3165 9.95246 22.7148 9.95246C25.813 9.95377 28.7255 11.1613 30.9157 13.3532C33.1059 15.545 34.3112 18.4585 34.3099 21.5571C34.3073 27.9514 29.1036 33.1535 22.7102 33.1535ZM29.0729 24.4685C28.7242 24.2942 27.0098 23.4507 26.6901 23.3343C26.3703 23.218 26.138 23.16 25.9056 23.5087C25.6732 23.8578 25.0047 24.6429 24.8013 24.8756C24.598 25.1083 24.3946 25.1374 24.0458 24.963C23.6971 24.7886 22.5736 24.4203 21.2416 23.2326C20.2048 22.3083 19.505 21.1666 19.3017 20.8175C19.0983 20.4685 19.2802 20.2798 19.4545 20.106C19.6113 19.9499 19.8033 19.699 19.9776 19.4952C20.152 19.2915 20.21 19.1462 20.3264 18.9135C20.4427 18.6808 20.3844 18.4774 20.2974 18.3027C20.2103 18.1283 19.5129 16.4123 19.2221 15.7145C18.9389 15.0346 18.6514 15.1265 18.4376 15.1161C18.2346 15.106 18.0019 15.1037 17.7692 15.1037C17.5365 15.1037 17.159 15.1911 16.8393 15.5398C16.5196 15.8889 15.6187 16.7324 15.6187 18.448C15.6187 20.164 16.8683 21.8217 17.0427 22.0544C17.217 22.2871 19.5018 25.8084 22.9996 27.3184C23.8317 27.6776 24.4813 27.8921 24.9874 28.0528C25.8228 28.3181 26.5832 28.2806 27.1839 28.191C27.854 28.0909 29.247 27.3475 29.5377 26.5333C29.8281 25.7191 29.8281 25.021 29.7411 24.8756C29.6541 24.7306 29.4217 24.6432 29.0729 24.4685Z"
                                                                    fill="white" />
                                                            </g>
                                                            <defs>
                                                                <linearGradient
                                                                    id="paint0_linear_979_268"
                                                                    x1="22"
                                                                    y1="40.8034" x2="22"
                                                                    y2="-3.19658"
                                                                    gradientUnits="userSpaceOnUse">
                                                                    <stop stop-color="#78CD51" />
                                                                    <stop offset="1"
                                                                        stop-color="#A0FC84" />
                                                                </linearGradient>
                                                                <clipPath id="clip0_979_268">
                                                                    <rect width="44" height="44"
                                                                        fill="white" />
                                                                </clipPath>
                                                            </defs>
                                                        </svg>
                                                    </a>

                                                    <a href="#" class="text-decoration-none">
                                                        <svg width="44" height="44"
                                                            viewBox="0 0 44 44" fill="none"
                                                            xmlns="http://www.w3.org/2000/svg">
                                                            <rect x="0.5" y="0.5" width="43"
                                                                height="43" rx="21.5"
                                                                fill="white" stroke="#BBC8D9" />
                                                            <g clip-path="url(#clip0_0_1)">
                                                                <path
                                                                    d="M23.7612 20.2364C23.5717 20.0458 23.3615 19.8768 23.1346 19.7325C22.5337 19.3477 21.8352 19.1433 21.1217 19.1433C20.1313 19.1414 19.1812 19.5349 18.4821 20.2364L15.0907 23.6305C14.3929 24.3297 14.0006 25.277 14 26.2649C13.9987 28.3264 15.6688 29.9987 17.7303 30C18.7191 30.0034 19.6683 29.6118 20.3671 28.9122L23.1666 26.1127C23.2175 26.0622 23.2459 25.9934 23.2455 25.9218C23.2447 25.7746 23.1246 25.6559 22.9773 25.6568H22.8707C22.2858 25.6588 21.7061 25.5465 21.1643 25.3262C21.0645 25.2851 20.9498 25.3083 20.8737 25.3848L18.8608 27.4004C18.2357 28.0255 17.2222 28.0255 16.5972 27.4004C15.9721 26.7754 15.9721 25.7619 16.5972 25.1368L20.0019 21.7348C20.6265 21.111 21.6383 21.111 22.2628 21.7348C22.6838 22.1309 23.3403 22.1309 23.7612 21.7348C23.9423 21.5535 24.0521 21.3132 24.0705 21.0576C24.09 20.7522 23.9773 20.4531 23.7612 20.2364Z"
                                                                    fill="#BBC8D9" />
                                                                <path
                                                                    d="M28.9065 15.0934C27.4487 13.6356 25.0852 13.6356 23.6274 15.0934L20.8306 17.8875C20.7538 17.9647 20.7316 18.0808 20.7746 18.1808C20.8171 18.2811 20.9163 18.3455 21.0252 18.3434H21.1239C21.7081 18.3424 22.2868 18.4556 22.8276 18.6767C22.9274 18.7178 23.0421 18.6946 23.1182 18.6181L25.1258 16.6131C25.7509 15.988 26.7643 15.988 27.3894 16.6131C28.0145 17.2382 28.0145 18.2516 27.3894 18.8767L24.8885 21.3749L24.8672 21.3989L23.9927 22.2681C23.3681 22.8919 22.3563 22.8919 21.7317 22.2681C21.3108 21.8719 20.6542 21.8719 20.2333 22.2681C20.0511 22.4507 19.9412 22.6932 19.924 22.9506C19.9045 23.256 20.0172 23.5551 20.2333 23.7718C20.5419 24.0817 20.903 24.3344 21.2998 24.5183C21.3558 24.545 21.4118 24.5663 21.4678 24.5903C21.5238 24.6143 21.5824 24.633 21.6384 24.6543C21.6944 24.6756 21.753 24.6943 21.809 24.7103L21.9663 24.7529C22.073 24.7796 22.1796 24.8009 22.2889 24.8196C22.4206 24.8391 22.5532 24.8516 22.6862 24.8569H22.8728H22.8888L23.0488 24.8383C23.1074 24.8356 23.1688 24.8223 23.2381 24.8223H23.3287L23.5127 24.7956L23.598 24.7796L23.7527 24.7476H23.782C24.4369 24.5831 25.0349 24.244 25.5124 23.7665L28.9064 20.3724C30.3643 18.9146 30.3643 16.5511 28.9065 15.0934Z"
                                                                    fill="#BBC8D9" />
                                                            </g>
                                                            <defs>
                                                                <clipPath id="clip0_0_1">
                                                                    <rect width="16" height="16"
                                                                        fill="white"
                                                                        transform="translate(14 14)" />
                                                                </clipPath>
                                                            </defs>
                                                        </svg>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
                <br />
            </t>
        </t>
    </template>
</odoo>