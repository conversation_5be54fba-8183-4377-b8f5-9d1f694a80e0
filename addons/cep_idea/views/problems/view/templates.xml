<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="cep_idea.problems_view" name="Problem">
        <t t-call="website.layout">
            <t t-if=" problem != undefined ">
                <t t-set="title">Problem Details</t>
                <t t-set="head">
                    <t t-call-assets="web.assets_common" />
                    <t t-call-assets="web.assets_frontend" />
                    <t t-call-assets="cep_idea.cep_idea_common" />
                    <t t-call-assets="cep_idea.problem_details" />
                </t>
                <div class="oe_structure">
                    <section class="s_problem_details pt-3 bg-white">
                        <div class="container">
                            <div class="row my-4">
                                <div class="col-12 col-md-8">
                                    <div class="d-flex flex-column gap-4 mb-3">
                                        <!-- cover image -->
                                        <t t-if="problem.cover_photo">
                                            <div class="featured-image"
                                                t-attf-style="background-image: url('{{ image_data_uri(problem.cover_photo) }}');"
                                                alt="problem cover"></div>
                                        </t>
                                        <t t-else="">
                                            <div class="featured-image"
                                                t-attf-style="background-image: url('/cep_idea/static/src/img/placeholder.png');"
                                                alt="problem cover"></div>
                                        </t>
                                        <div class="d-flex flex-row align-items-center gap-2">
                                            <svg width="80" height="80" viewBox="0 0 50 50"
                                                fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <rect width="50" height="50" rx="25" fill="#F2F2F2" />
                                                <path
                                                    d="M33.6668 34.75V32.5833C33.6668 31.4341 33.2103 30.3319 32.3976 29.5192C31.585 28.7065 30.4828 28.25 29.3335 28.25H20.6668C19.5176 28.25 18.4154 28.7065 17.6027 29.5192C16.79 30.3319 16.3335 31.4341 16.3335 32.5833V34.75"
                                                    stroke="#1A2942" stroke-width="2"
                                                    stroke-linecap="round" stroke-linejoin="round" />
                                                <path
                                                    d="M24.9998 23.9167C27.3931 23.9167 29.3332 21.9766 29.3332 19.5833C29.3332 17.1901 27.3931 15.25 24.9998 15.25C22.6066 15.25 20.6665 17.1901 20.6665 19.5833C20.6665 21.9766 22.6066 23.9167 24.9998 23.9167Z"
                                                    stroke="#1A2942" stroke-width="2"
                                                    stroke-linecap="round" stroke-linejoin="round" />
                                            </svg>
                                            <div class="d-flex flex-column">
                                                <div
                                                    class="d-flex flex-column flex-md-row align-items-md-center gap-2">
                                                    <span class="author">
                                                        <t t-esc="problem.owner_id.name" />
                                                    </span>
                                                    <span class="date">
                                                        <t
                                                            t-esc="problem.create_date.strftime('%d %b %Y')" />
                                                    </span>
                                                </div>
                                                <div
                                                    class="d-flex flex-row align-items-center gap-2">
                                                    <svg xmlns="http://www.w3.org/2000/svg"
                                                        width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none">
                                                        <path
                                                            d="M12.398 17.804L12.8585 18.6917L12.8585 18.6917L12.398 17.804ZM11.602 17.804L11.1415 18.6917L11.1415 18.6917L11.602 17.804ZM18 9C18 11.1458 16.9079 12.9159 15.545 14.2906C14.183 15.6644 12.6342 16.555 11.9376 16.9163L12.8585 18.6917C13.6448 18.2838 15.397 17.2805 16.9653 15.6987C18.5326 14.1178 20 11.8706 20 9H18ZM12 3C15.3137 3 18 5.68629 18 9H20C20 4.58172 16.4183 1 12 1V3ZM6 9C6 5.68629 8.68629 3 12 3V1C7.58172 1 4 4.58172 4 9H6ZM12.0624 16.9163C11.3658 16.555 9.81702 15.6644 8.45503 14.2906C7.09211 12.9159 6 11.1458 6 9H4C4 11.8706 5.46741 14.1178 7.03474 15.6987C8.60299 17.2805 10.3552 18.2838 11.1415 18.6917L12.0624 16.9163ZM11.9376 16.9163C11.9514 16.9091 11.9733 16.9023 12 16.9023C12.0267 16.9023 12.0486 16.9091 12.0624 16.9163L11.1415 18.6917C11.6831 18.9726 12.3169 18.9726 12.8585 18.6917L11.9376 16.9163ZM14 9C14 10.1046 13.1046 11 12 11V13C14.2091 13 16 11.2091 16 9H14ZM12 7C13.1046 7 14 7.89543 14 9H16C16 6.79086 14.2091 5 12 5V7ZM10 9C10 7.89543 10.8954 7 12 7V5C9.79086 5 8 6.79086 8 9H10ZM12 11C10.8954 11 10 10.1046 10 9H8C8 11.2091 9.79086 13 12 13V11Z"
                                                            fill="#1FCCC6" />
                                                        <path
                                                            d="M19.7942 17.5C20.5841 17.9561 21 18.4734 21 19C21 19.5266 20.5841 20.0439 19.7942 20.5C19.0043 20.9561 17.8682 21.3348 16.5 21.5981C15.1318 21.8614 13.5798 22 12 22C10.4202 22 8.86817 21.8614 7.5 21.5981C6.13183 21.3348 4.99569 20.9561 4.20577 20.5C3.41586 20.0439 3 19.5266 3 19C3 18.4734 3.41586 17.9561 4.20577 17.5"
                                                            stroke="#1FCCC6" stroke-width="2"
                                                            stroke-linecap="round" />
                                                    </svg>
                                                    <span class="location">
                                                        <t t-esc="problem.location" />
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <h3 class="fw-700 c-dark-gunmetal">
                                            <t t-esc="problem.title" />
                                        </h3>
                                        <div class="text-justify">
                                            <t t-raw="problem.description" />
                                        </div>
                                        <!-- <a href="#">Read More</a> -->
                                    </div>
                                </div>

                                <div class="col-12 col-md-4">
                                    <div class="card">
                                        <div class="card-header">
                                            About
                                        </div>
                                        <div class="card-body">
                                            <div class="d-flex flex-column gap-2">
                                                <div class="d-flex flex-row gap-2">
                                                    <svg xmlns="http://www.w3.org/2000/svg"
                                                        width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none">
                                                        <circle cx="12" cy="12" r="9"
                                                            stroke="#33363F" stroke-width="2" />
                                                        <path
                                                            d="M16.5 12H12.25C12.1119 12 12 11.8881 12 11.75V8.5"
                                                            stroke="#33363F" stroke-width="2"
                                                            stroke-linecap="round" />
                                                    </svg>
                                                    <span class="summary">Ends on <t
                                                            t-esc="problem.get_end_date().strftime('%d %b %Y')" /></span>
                                                </div>

                                                <div class="d-flex flex-row gap-2">
                                                    <svg xmlns="http://www.w3.org/2000/svg"
                                                        width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none">
                                                        <path
                                                            d="M12.398 17.804L12.8585 18.6917L12.8585 18.6917L12.398 17.804ZM11.602 17.804L11.1415 18.6917L11.1415 18.6917L11.602 17.804ZM18 9C18 11.1458 16.9079 12.9159 15.545 14.2906C14.183 15.6644 12.6342 16.555 11.9376 16.9163L12.8585 18.6917C13.6448 18.2838 15.397 17.2805 16.9653 15.6987C18.5326 14.1178 20 11.8706 20 9H18ZM12 3C15.3137 3 18 5.68629 18 9H20C20 4.58172 16.4183 1 12 1V3ZM6 9C6 5.68629 8.68629 3 12 3V1C7.58172 1 4 4.58172 4 9H6ZM12.0624 16.9163C11.3658 16.555 9.81702 15.6644 8.45503 14.2906C7.09211 12.9159 6 11.1458 6 9H4C4 11.8706 5.46741 14.1178 7.03474 15.6987C8.60299 17.2805 10.3552 18.2838 11.1415 18.6917L12.0624 16.9163ZM11.9376 16.9163C11.9514 16.9091 11.9733 16.9023 12 16.9023C12.0267 16.9023 12.0486 16.9091 12.0624 16.9163L11.1415 18.6917C11.6831 18.9726 12.3169 18.9726 12.8585 18.6917L11.9376 16.9163ZM14 9C14 10.1046 13.1046 11 12 11V13C14.2091 13 16 11.2091 16 9H14ZM12 7C13.1046 7 14 7.89543 14 9H16C16 6.79086 14.2091 5 12 5V7ZM10 9C10 7.89543 10.8954 7 12 7V5C9.79086 5 8 6.79086 8 9H10ZM12 11C10.8954 11 10 10.1046 10 9H8C8 11.2091 9.79086 13 12 13V11Z"
                                                            fill="#33363F" />
                                                        <path
                                                            d="M19.7942 17.5C20.5841 17.9561 21 18.4734 21 19C21 19.5266 20.5841 20.0439 19.7942 20.5C19.0043 20.9561 17.8682 21.3348 16.5 21.5981C15.1318 21.8614 13.5798 22 12 22C10.4202 22 8.86817 21.8614 7.5 21.5981C6.13183 21.3348 4.99569 20.9561 4.20577 20.5C3.41586 20.0439 3 19.5266 3 19C3 18.4734 3.41586 17.9561 4.20577 17.5"
                                                            stroke="#33363F" stroke-width="2"
                                                            stroke-linecap="round" />
                                                    </svg>
                                                    <span class="summary">
                                                        <t t-esc="problem.location" />
                                                    </span>
                                                </div>

                                                <div class="d-flex flex-row gap-2">
                                                    <svg xmlns="http://www.w3.org/2000/svg"
                                                        width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none">
                                                        <circle cx="12" cy="8" r="3"
                                                            stroke="#33363F" stroke-width="2"
                                                            stroke-linecap="round" />
                                                        <path
                                                            d="M15.2679 8C15.5332 7.54063 15.97 7.20543 16.4824 7.06815C16.9947 6.93086 17.5406 7.00273 18 7.26795C18.4594 7.53317 18.7946 7.97 18.9319 8.48236C19.0691 8.99472 18.9973 9.54063 18.7321 10C18.4668 10.4594 18.03 10.7946 17.5176 10.9319C17.0053 11.0691 16.4594 10.9973 16 10.7321C15.5406 10.4668 15.2054 10.03 15.0681 9.51764C14.9309 9.00528 15.0027 8.45937 15.2679 8L15.2679 8Z"
                                                            stroke="#33363F" stroke-width="2" />
                                                        <path
                                                            d="M5.26795 8C5.53317 7.54063 5.97 7.20543 6.48236 7.06815C6.99472 6.93086 7.54063 7.00273 8 7.26795C8.45937 7.53317 8.79457 7.97 8.93185 8.48236C9.06914 8.99472 8.99727 9.54063 8.73205 10C8.46683 10.4594 8.03 10.7946 7.51764 10.9319C7.00528 11.0691 6.45937 10.9973 6 10.7321C5.54063 10.4668 5.20543 10.03 5.06815 9.51764C4.93086 9.00528 5.00273 8.45937 5.26795 8L5.26795 8Z"
                                                            stroke="#33363F" stroke-width="2" />
                                                        <path
                                                            d="M16.8816 18L15.9012 18.1974L16.0629 19H16.8816V18ZM20.7201 16.9042L21.6626 16.5699L20.7201 16.9042ZM14.7808 14.7105L14.1759 13.9142L13.0193 14.7927L14.2526 15.5597L14.7808 14.7105ZM19.8672 17H16.8816V19H19.8672V17ZM19.7777 17.2384C19.7706 17.2186 19.7641 17.181 19.7725 17.1354C19.7803 17.0921 19.7982 17.0593 19.8151 17.0383C19.8473 16.9982 19.8739 17 19.8672 17V19C21.0132 19 22.1413 17.9194 21.6626 16.5699L19.7777 17.2384ZM17 15C18.6415 15 19.4026 16.1811 19.7777 17.2384L21.6626 16.5699C21.1976 15.2588 19.9485 13 17 13V15ZM15.3856 15.5069C15.7701 15.2148 16.2819 15 17 15V13C15.8381 13 14.9027 13.3622 14.1759 13.9142L15.3856 15.5069ZM14.2526 15.5597C15.2918 16.206 15.727 17.3324 15.9012 18.1974L17.8619 17.8026C17.6439 16.7204 17.0374 14.9364 15.3089 13.8614L14.2526 15.5597Z"
                                                            fill="#33363F" />
                                                        <path
                                                            d="M9.21924 14.7105L9.74736 15.5597L10.9807 14.7927L9.82409 13.9142L9.21924 14.7105ZM3.27986 16.9041L4.22233 17.2384L4.22233 17.2384L3.27986 16.9041ZM7.11841 18V19H7.93709L8.09873 18.1974L7.11841 18ZM7.00008 15C7.71809 15 8.22992 15.2148 8.61439 15.5069L9.82409 13.9142C9.09729 13.3621 8.16196 13 7.00008 13V15ZM4.22233 17.2384C4.59738 16.1811 5.35849 15 7.00008 15V13C4.05157 13 2.80244 15.2587 2.33739 16.5699L4.22233 17.2384ZM4.13284 17C4.12606 17 4.1527 16.9982 4.18492 17.0383C4.20182 17.0593 4.21967 17.0921 4.22754 17.1354C4.23586 17.181 4.22937 17.2186 4.22233 17.2384L2.33739 16.5699C1.8587 17.9194 2.98683 19 4.13284 19V17ZM7.11841 17H4.13284V19H7.11841V17ZM8.09873 18.1974C8.27295 17.3324 8.7082 16.206 9.74736 15.5597L8.69112 13.8614C6.96263 14.9363 6.35606 16.7203 6.1381 17.8026L8.09873 18.1974Z"
                                                            fill="#33363F" />
                                                        <path
                                                            d="M12 14C15.5715 14 16.5919 16.5512 16.8834 18.0089C16.9917 18.5504 16.5523 19 16 19H8C7.44772 19 7.00829 18.5504 7.11659 18.0089C7.4081 16.5512 8.42846 14 12 14Z"
                                                            stroke="#33363F" stroke-width="2"
                                                            stroke-linecap="round" />
                                                    </svg>
                                                    <span class="summary"><t
                                                            t-esc="problem.get_participant_count()" />
                                                        Participants</span>
                                                </div>

                                                <div class="d-flex flex-row gap-2">
                                                    <svg xmlns="http://www.w3.org/2000/svg"
                                                        width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none">
                                                        <path
                                                            d="M21 6L15.7071 11.2929C15.3166 11.6834 14.6834 11.6834 14.2929 11.2929L12.7071 9.70711C12.3166 9.31658 11.6834 9.31658 11.2929 9.70711L7 14"
                                                            stroke="#33363F" stroke-width="2"
                                                            stroke-linecap="round"
                                                            stroke-linejoin="round" />
                                                        <path
                                                            d="M3 3V17.8C3 18.9201 3 19.4802 3.21799 19.908C3.40973 20.2843 3.71569 20.5903 4.09202 20.782C4.51984 21 5.07989 21 6.2 21H21"
                                                            stroke="#33363F" stroke-width="2"
                                                            stroke-linecap="round" />
                                                    </svg>
                                                    <span class="summary"><t
                                                            t-esc="problem.get_phase_count()" />
                                                        Phases</span>
                                                </div>

                                                <div class="d-flex flex-row gap-2">
                                                    <svg xmlns="http://www.w3.org/2000/svg"
                                                        width="24" height="24" viewBox="0 0 24 24"
                                                        fill="none">
                                                        <path
                                                            d="M17.5 8C18.8807 8 20 6.88071 20 5.5C20 4.11929 18.8807 3 17.5 3C16.1193 3 15 4.11929 15 5.5C15 6.88071 16.1193 8 17.5 8Z"
                                                            stroke="#181C32" stroke-width="2"
                                                            stroke-linecap="round"
                                                            stroke-linejoin="round" />
                                                        <path
                                                            d="M6 15C7.65685 15 9 13.6569 9 12C9 10.3431 7.65685 9 6 9C4.34315 9 3 10.3431 3 12C3 13.6569 4.34315 15 6 15Z"
                                                            stroke="#181C32" stroke-width="2"
                                                            stroke-linecap="round"
                                                            stroke-linejoin="round" />
                                                        <path
                                                            d="M17.5 21C18.8807 21 20 19.8807 20 18.5C20 17.1193 18.8807 16 17.5 16C16.1193 16 15 17.1193 15 18.5C15 19.8807 16.1193 21 17.5 21Z"
                                                            stroke="#181C32" stroke-width="2"
                                                            stroke-linecap="round"
                                                            stroke-linejoin="round" />
                                                        <path d="M9 13L15 17" stroke="#181C32"
                                                            stroke-width="2" stroke-linecap="round"
                                                            stroke-linejoin="round" />
                                                        <path d="M15 7L9 11" stroke="#181C32"
                                                            stroke-width="2" stroke-linecap="round"
                                                            stroke-linejoin="round" />
                                                    </svg>
                                                    <span class="summary">Share</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="card mt-4">
                                        <div class="card-body">
                                            <div class="d-flex flex-row gap-2">
                                                <t t-set="today" t-value="datetime.date.today()" />
                                                <t t-if="problem.get_end_date() &gt;= today">
                                                    <svg xmlns="http://www.w3.org/2000/svg"
                                                        width="24" height="24" viewBox="0 0 8 8"
                                                        fill="none">
                                                        <circle cx="4" cy="4" r="4" fill="#7ABF45" />
                                                    </svg>
                                                    <span class="status">This problem is currently
                                                        open for participation.</span>
                                                </t>
                                                <t t-else="">
                                                    <svg xmlns="http://www.w3.org/2000/svg"
                                                        width="24" height="24" viewBox="0 0 8 8"
                                                        fill="none">
                                                        <circle cx="4" cy="4" r="4" fill="#FF0000" />
                                                    </svg>
                                                    <span class="status">This problem is closed.</span>
                                                </t>
                                            </div>
                                            <t t-if="not phases[0]['phase'].is_deadline_pass()">
                                                <div class="d-flex justify-content-center mt-4">
                                                    <a
                                                        t-att-href="'phases/' + str(phases[0]['phase'].id) + '/ideas/new'"
                                                        class="btn btn-lg btn-submit-idea">Submit
                                                        your idea</a>
                                                </div>
                                            </t>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <section class="s_problem_phases bg-alice-blue pt-3 pb-1">
                        <div class="container">
                            <div class="d-flex flex-column my-4">
                                <h3 class="fw-700 c-dark-gunmetal">Phases</h3>
                                <div class="phases d-flex flex-row my-4">
                                    <t t-set="phase_counter" t-value="1" />
                                    <t t-set="phase_id" t-value="" />
                                    <t t-foreach="phases" t-as="phase">
                                        <t t-if="phase_counter == 1">
                                            <t t-set="phase_id" t-value="phase['phase'].id" />
                                        </t>
                                        <a href="#"
                                            t-attf-class="phase flex-fill py-2 #{phase_counter==1 and 'active' or ''}"
                                            t-att-phase-id="phase['phase'].id"
                                            t-att-phase-serial="phase_counter">
                                            <span class="d-inline d-md-none"><t
                                                    t-esc="phase_counter" />.</span>
                                            <span class="d-none d-md-inline"><t
                                                    t-esc="phase_counter" />. <t
                                                    t-esc="phase['phase'].name" /></span>
                                        </a>
                                        <t t-set="phase_counter" t-value="phase_counter + 1" />
                                    </t>
                                </div>

                                <div class="d-flex p-4 phase-details">
                                    <t t-set="phase_counter" t-value="1" />
                                    <t t-foreach="phases" t-as="phase">
                                        <t t-if="phase['phase'].id == phase_id">
                                            <div class="flex-shrink-0">
                                                <span class="phase-number"><t t-esc="phase_counter" />
                                                    .</span>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <div class="d-flex flex-column gap-1">
                                                    <span class="phase-name">
                                                        <t t-esc="phase['phase'].name" />
                                                    </span>
                                                    <span class="phase-timeline"><t
                                                            t-esc="phase['phase'].start_date.strftime('%d %b %Y')" />
                                                        - <t
                                                            t-esc="phase['phase'].end_date.strftime('%d %b %Y')" /></span>
                                                    <span class="phase-description text-justify">
                                                        <t t-esc="phase['phase'].description" />
                                                    </span>

                                                    <!-- <div class="callout">
                                                        <p class="callout-title">Final Results</p>
                                                        <p class="callout-text"><b>Submitting budgets closed on December 21, 2023.</b> Participants had a
                                                    total of <b>100,000 CRE each to distribute
                                                    between 4 options.</b></p>
                                                    </div> -->
                                                </div>
                                            </div>
                                        </t>
                                    </t>

                                </div>
                            </div>
                        </div>
                    </section>

                    <section class="s_problem_ideas bg-alice-blue pt-3 pb-1">
                        <div class="container">
                            <div
                                class="d-flex flex-column flex-md-row justify-content-md-between my-4 gap-3">
                                <h3 class="fw-700 c-dark-gunmetal" id="idea-count">Submitted Idea (<t
                                        t-esc="idea_count" />)</h3>

                                <div class="mx-auto w-100 w-md-25">
                                    <!-- <div class="search input-group">
                                        <input class="form-control" id="search-text" type="text" placeholder="Search ..."/>
                                        <span class="input-group-append">
                                            <button class="btn" type="button" id="search-btn">
                                                <i class="fa fa-search"></i>
                                            </button>
                                        </span>
                                    </div> -->
                                </div>

                                <div class="d-flex flex-row gap-3">
                                    <div class="search input-group">
                                        <input class="form-control" id="search-text" type="text"
                                            placeholder="Search ..." />
                                        <span class="input-group-append">
                                            <button class="btn" type="button" id="search-btn">
                                                <i class="fa fa-search"></i>
                                            </button>
                                        </span>
                                    </div>
                                    <!-- <div class="dropdown">
                                        <button class="btn btn-md btn-filter dropdown-toggle" type="button" id="dropdownMenuButton1"
                                            data-bs-toggle="dropdown" aria-expanded="false">
                                            Trending
                                        </button>
                                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                            <li><a class="dropdown-item" href="#">Action</a></li>
                                            <li><a class="dropdown-item" href="#">Another action</a></li>
                                            <li><a class="dropdown-item" href="#">Something else here</a></li>
                                        </ul>
                                    </div>
    
                                    <div class="dropdown">
                                        <button class="btn btn-md btn-filter dropdown-toggle" type="button" id="dropdownMenuButton1"
                                            data-bs-toggle="dropdown" aria-expanded="false">
                                            Tags
                                        </button>
                                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                            <li><a class="dropdown-item" href="#">Action</a></li>
                                            <li><a class="dropdown-item" href="#">Another action</a></li>
                                            <li><a class="dropdown-item" href="#">Something else here</a></li>
                                        </ul>
                                    </div> -->

                                </div>
                            </div>

                            <div class="row mt-4 idea-container">
                                <!-- idea card -->
                                <t t-foreach="phases" t-as="phase">
                                    <t t-if="phase['phase'].id == phase_id">
                                        <t t-foreach="phase['ideas']" t-as="idea">
                                            <div class="col-12 col-md-6 mt-4">
                                                <div class="card card-idea">
                                                    <div class="card-body">
                                                        <div
                                                            class="d-flex flex-column flex-md-row gap-3">
                                                            <t t-if=" idea.cover_photo ">
                                                                <img class="featured-image"
                                                                    t-att-src="image_data_uri(idea.cover_photo)"
                                                                    alt="..." />
                                                            </t>
                                                            <t t-else="">
                                                                <img
                                                                    src="https://via.placeholder.com/160x160?text=No+Image"
                                                                    class="featured-image" alt="..."></img>
                                                            </t>

                                                            <div class="d-flex flex-column gap-1">
                                                                <div
                                                                    class="d-flex flex-row align-items-center gap-1">
                                                                    <svg width="50" height="50"
                                                                        viewBox="0 0 50 50"
                                                                        fill="none"
                                                                        xmlns="http://www.w3.org/2000/svg">
                                                                        <rect width="50" height="50"
                                                                            rx="25" fill="#F2F2F2" />
                                                                        <path
                                                                            d="M33.6668 34.75V32.5833C33.6668 31.4341 33.2103 30.3319 32.3976 29.5192C31.585 28.7065 30.4828 28.25 29.3335 28.25H20.6668C19.5176 28.25 18.4154 28.7065 17.6027 29.5192C16.79 30.3319 16.3335 31.4341 16.3335 32.5833V34.75"
                                                                            stroke="#1A2942"
                                                                            stroke-width="2"
                                                                            stroke-linecap="round"
                                                                            stroke-linejoin="round" />
                                                                        <path
                                                                            d="M24.9998 23.9167C27.3931 23.9167 29.3332 21.9766 29.3332 19.5833C29.3332 17.1901 27.3931 15.25 24.9998 15.25C22.6066 15.25 20.6665 17.1901 20.6665 19.5833C20.6665 21.9766 22.6066 23.9167 24.9998 23.9167Z"
                                                                            stroke="#1A2942"
                                                                            stroke-width="2"
                                                                            stroke-linecap="round"
                                                                            stroke-linejoin="round" />
                                                                    </svg>

                                                                    <span><t
                                                                            t-esc="idea.owner_id.name" />
                                                                        - <t
                                                                            t-esc="idea.create_date.strftime('%d %b %Y')" /></span>
                                                                </div>
                                                                <a
                                                                    class="link-idea text-decoration-underline"
                                                                    t-attf-href="/ideas/#{str(idea.id)}/view"><t
                                                                        t-esc="idea.title[:48]" />
                                                                    ...</a>
                                                                <p><t t-esc="idea.description[:48]" />
                                                                    ...</p>

                                                                <div
                                                                    class="d-flex flex-column flex-md-row align-items-md-center justify-content-md-between gap-3">
                                                                    <div
                                                                        class="d-flex flex-row align-items-center gap-2">
                                                                        <a
                                                                            t-att-href="'/ideas/' + str(idea.id) + '/reactions/like/create'"
                                                                            class="d-flex flex-row align-items-center gap-2 text-decoration-none">
                                                                            <svg width="32"
                                                                                height="32"
                                                                                viewBox="0 0 32 32"
                                                                                fill="none"
                                                                                xmlns="http://www.w3.org/2000/svg">
                                                                                <rect width="32"
                                                                                    height="32"
                                                                                    rx="16"
                                                                                    fill="#EEF3FE" />
                                                                                <g opacity="0.8">
                                                                                    <path
                                                                                        d="M9.51306 23.9999C9.37609 23.9999 9.24727 23.9461 9.1504 23.8485C9.05347 23.7508 9 23.621 9 23.4828V15.898C9 15.6128 9.2304 15.3808 9.51347 15.3808L12.4909 15.3806C12.6282 15.3806 12.7572 15.4345 12.8541 15.5322C12.951 15.6297 13.0045 15.7598 13.0045 15.898V23.4829C13.0046 23.621 12.9511 23.7509 12.854 23.8487C12.757 23.9463 12.6281 24 12.4909 24L9.51306 23.9999Z"
                                                                                        stroke="#181C32"
                                                                                        stroke-width="1.5" />
                                                                                    <path
                                                                                        d="M14.6009 23.9763C14.2783 23.9763 14.0051 23.7985 13.929 23.744C13.8393 23.6799 13.6581 23.5159 13.5566 23.4226C13.5566 23.4226 13.5566 15.7835 13.5566 15.758C13.5566 15.758 13.6472 15.4374 13.8973 15.2028C15.2159 13.9659 17.4813 12.2801 18.3041 10.6239C18.5887 10.0508 18.8295 9.45015 19.0189 8.83931C19.1624 8.3767 19.3533 8.09328 19.87 8.01382C20.4077 7.93113 20.9477 8.22836 21.2171 8.6672C21.8173 9.64537 21.5461 10.8196 21.1319 11.8101C20.9117 12.3369 20.6397 12.8331 20.3621 13.331C20.2848 13.4698 19.6752 14.495 19.6752 14.495C21.0617 14.4991 22.9846 14.5047 22.9846 14.5047C23.6821 14.5105 24.9994 14.8784 24.9996 16.2296C24.9996 16.8899 24.6191 17.2808 24.3056 17.494C24.6055 17.7325 24.952 18.1436 24.8703 18.7918C24.7915 19.4182 24.4697 19.774 24.1069 19.9408C24.3169 20.1895 24.4706 20.5684 24.382 21.0998C24.2984 21.6011 23.9783 21.8896 23.6657 22.0402C23.8248 22.2534 23.9342 22.5665 23.855 22.9897C23.6859 23.8936 22.9031 23.9466 22.5688 23.9692C22.539 23.9712 22.5115 23.973 22.4868 23.9753L22.476 23.9763H14.6009V23.9763Z"
                                                                                        stroke="#181C32"
                                                                                        stroke-width="1.5" />
                                                                                </g>
                                                                            </svg>
                                                                            <span>
                                                                                <t
                                                                                    t-esc="idea.count_like()" />
                                                                            </span>
                                                                        </a>
                                                                        <a
                                                                            t-att-href="'/ideas/' + str(idea.id) + '/reactions/dislike/create'"
                                                                            class="d-flex flex-row align-items-center gap-2 text-decoration-none">
                                                                            <svg
                                                                                xmlns="http://www.w3.org/2000/svg"
                                                                                width="30"
                                                                                height="30"
                                                                                viewBox="0 0 30 30"
                                                                                fill="none">
                                                                                <rect width="30"
                                                                                    height="30"
                                                                                    rx="15"
                                                                                    fill="#F2F2F2" />
                                                                                <g opacity="0.6">
                                                                                    <path
                                                                                        d="M22.25 8.75012L22.25 15.8692L19.7455 15.8694L19.7455 8.74999L22.25 8.75012Z"
                                                                                        stroke="#181C32"
                                                                                        stroke-width="1.5" />
                                                                                    <path
                                                                                        d="M12.3243 17.505L12.969 17.8883L13.6406 16.7588L12.3265 16.755L10.1519 16.7486L9.33921 16.7462L9.10243 16.7455L9.03903 16.7453L9.02265 16.7453L9.01926 16.7453C8.77073 16.7429 8.41389 16.6712 8.1448 16.5022C7.91259 16.3564 7.74998 16.15 7.74989 15.7704L12.3243 17.505ZM12.3243 17.505L12.969 17.8883M12.3243 17.505L12.969 17.8883M8.35732 11.0235C8.32978 11.1888 8.34347 11.3051 8.36555 11.3843C8.38789 11.4644 8.42437 11.5264 8.46561 11.5753L9.10181 12.3287L8.2059 12.7406C8.13375 12.7738 8.06742 12.8242 8.01274 12.9C7.95837 12.9754 7.89882 13.0992 7.8733 13.3019L7.87329 13.302C7.85286 13.4641 7.88532 13.5736 7.93024 13.6578C7.98073 13.7525 8.06139 13.84 8.16069 13.9189L8.9583 14.5531L8.11573 15.1261C7.91737 15.261 7.74993 15.4505 7.74989 15.7702L8.35732 11.0235ZM8.35732 11.0235C8.37631 10.9097 8.41774 10.8358 8.46314 10.782C8.51229 10.7237 8.5795 10.674 8.65927 10.6356L9.47911 10.2407L8.935 9.51137C8.91199 9.48053 8.89164 9.4417 8.87965 9.39106C8.86786 9.34124 8.86017 9.26325 8.88167 9.14823C8.90434 9.02708 8.94054 8.96705 8.96311 8.93786C8.98634 8.90782 9.01635 8.8832 9.06053 8.86115C9.16883 8.80711 9.30674 8.7909 9.48132 8.77909L9.48158 8.77907C9.50176 8.7777 9.52884 8.77592 9.55627 8.77372L17.3986 8.77372L17.4022 8.77372C17.4297 8.77446 17.4732 8.78343 17.5284 8.80741C17.5839 8.8315 17.6245 8.85906 17.6338 8.86572L17.6342 8.866C17.6338 8.86576 17.637 8.86809 17.6445 8.87396C17.6521 8.87989 17.6618 8.88774 17.6737 8.89764C17.6798 8.90269 17.6862 8.90804 17.6929 8.91366L17.6929 8.91557L17.6929 8.92556L17.6929 8.93567L17.6929 8.94592L17.6929 8.9563L17.6929 8.96681L17.6929 8.97744L17.6929 8.9882L17.6929 8.99908L17.6929 9.01009L17.6929 9.02122L17.6929 9.03248L17.6929 9.04386L17.6929 9.05536L17.6929 9.06699L17.6929 9.07873L17.6929 9.0906L17.6929 9.10258L17.6929 9.11469L17.6929 9.12691L17.6929 9.13925L17.6929 9.15171L17.6929 9.16428L17.6929 9.17697L17.6929 9.18977L17.6929 9.20269L17.6929 9.21572L17.6929 9.22887L17.6929 9.24212L17.6929 9.25549L17.6929 9.26897L17.6929 9.28256L17.6929 9.29625L17.6929 9.31006L17.6929 9.32397L17.6929 9.338L17.6929 9.35212L17.6929 9.36636L17.6929 9.3807L17.6929 9.39514L17.6929 9.40969L17.6929 9.42434L17.6929 9.4391L17.6929 9.45395L17.6929 9.46891L17.6929 9.48397L17.6929 9.49912L17.6929 9.51438L17.6929 9.52974L17.6929 9.54519L17.6929 9.56074L17.6929 9.57639L17.6929 9.59213L17.6929 9.60797L17.6929 9.6239L17.6929 9.63992L17.6929 9.65604L17.6929 9.67225L17.6929 9.68855L17.6929 9.70495L17.6929 9.72143L17.6929 9.738L17.6929 9.75467L17.6929 9.77142L17.6929 9.78826L17.6929 9.80518L17.6929 9.82219L17.6929 9.83929L17.6929 9.85647L17.6929 9.87374L17.6929 9.89108L17.6929 9.90852L17.6929 9.92603L17.6929 9.94363L17.6929 9.9613L17.6929 9.97906L17.6929 9.99689L17.6929 10.0148L17.6929 10.0328L17.6929 10.0509L17.6929 10.069L17.6929 10.0872L17.6929 10.1055L17.6929 10.1239L17.6929 10.1424L17.6929 10.1609L17.6929 10.1795L17.6929 10.1981L17.6929 10.2169L17.6929 10.2357L17.6929 10.2545L17.6929 10.2735L17.6929 10.2925L17.6929 10.3116L17.6929 10.3307L17.6929 10.3499L17.6929 10.3692L17.6929 10.3886L17.6929 10.408L17.6929 10.4275L17.6929 10.447L17.6929 10.4666L17.6929 10.4863L17.6929 10.506L17.6929 10.5258L17.6929 10.5456L17.6929 10.5655L17.6929 10.5855L17.6929 10.6055L17.6929 10.6255L17.6929 10.6457L17.6929 10.6659L17.6929 10.6861L17.6929 10.7064L17.6929 10.7267L17.6929 10.7471L17.6929 10.7676L17.6929 10.7881L17.6929 10.8086L17.6929 10.8293L17.6929 10.8499L17.6929 10.8706L17.6929 10.8914L17.6929 10.9122L17.6929 10.933L17.6929 10.9539L17.6929 10.9748L17.6929 10.9958L17.6929 11.0168L17.6929 11.0379L17.6929 11.059L17.6929 11.0802L17.6929 11.1014L17.6929 11.1226L17.6929 11.1439L17.6929 11.1652L17.6929 11.1865L17.6929 11.2079L17.6929 11.2293L17.6929 11.2508L17.6929 11.2723L17.6929 11.2938L17.6929 11.3154L17.6929 11.337L17.6929 11.3586L17.6929 11.3803L17.6929 11.402L17.6929 11.4237L17.6929 11.4455L17.6929 11.4673L17.6929 11.4891L17.6929 11.5109L17.6929 11.5328L17.6929 11.5547L17.6929 11.5766L17.6929 11.5986L17.6929 11.6206L17.6929 11.6426L17.6929 11.6646L17.6929 11.6866L17.6929 11.7087L17.6929 11.7308L17.6929 11.7529L17.6929 11.7751L17.6929 11.7972L17.6929 11.8194L17.6929 11.8416L17.6929 11.8638L17.6929 11.886L17.6929 11.9082L17.6929 11.9305L17.6929 11.9528L17.6929 11.975L17.6929 11.9973L17.6929 12.0196L17.6929 12.042L17.6929 12.0643L17.6929 12.0866L17.6929 12.109L17.6929 12.1313L17.6929 12.1537L17.6929 12.1761L17.6929 12.1985L17.6929 12.2209L17.6929 12.2433L17.6929 12.2657L17.6929 12.2881L17.6929 12.3105L17.6929 12.3329L17.6929 12.3553L17.6929 12.3777L17.6929 12.4001L17.6929 12.4226L17.6929 12.445L17.6929 12.4674L17.6929 12.4898L17.6929 12.5122L17.6929 12.5346L17.6929 12.557L17.6929 12.5794L17.6929 12.6018L17.6929 12.6242L17.6929 12.6466L17.6929 12.669L17.6929 12.6913L17.6929 12.7137L17.6929 12.736L17.6929 12.7584L17.6929 12.7807L17.6929 12.803L17.6929 12.8253L17.6929 12.8476L17.6929 12.8699L17.6929 12.8922L17.6929 12.9144L17.6929 12.9367L17.6929 12.9589L17.6929 12.9811L17.6929 13.0033L17.6929 13.0254L17.6929 13.0476L17.6929 13.0697L17.6929 13.0918L17.6929 13.1139L17.6929 13.136L17.6929 13.158L17.6929 13.18L17.6929 13.202L17.6929 13.224L17.6929 13.246L17.6929 13.2679L17.6929 13.2898L17.6929 13.3117L17.6929 13.3335L17.6929 13.3553L17.6929 13.3771L17.6929 13.3989L17.6929 13.4206L17.6929 13.4423L17.6929 13.4639L17.6929 13.4856L17.6929 13.5072L17.6929 13.5287L17.6929 13.5503L17.6929 13.5717L17.6929 13.5932L17.6929 13.6146L17.6929 13.636L17.6929 13.6574L17.6929 13.6787L17.6929 13.6999L17.6929 13.7212L17.6929 13.7423L17.6929 13.7635L17.6929 13.7846L17.6929 13.8057L17.6929 13.8267L17.6929 13.8476L17.6929 13.8686L17.6929 13.8895L17.6929 13.9103L17.6929 13.9311L17.6929 13.9518L17.6929 13.9725L17.6929 13.9932L17.6929 14.0138L17.6929 14.0343L17.6929 14.0548L17.6929 14.0753L17.6929 14.0957L17.6929 14.116L17.6929 14.1363L17.6929 14.1565L17.6929 14.1767L17.6929 14.1968L17.6929 14.2169L17.6929 14.2369L17.6929 14.2568L17.6929 14.2767L17.6929 14.2966L17.6929 14.3163L17.6929 14.336L17.6929 14.3557L17.6929 14.3753L17.6929 14.3948L17.6929 14.4143L17.6929 14.4337L17.6929 14.453L17.6929 14.4723L17.6929 14.4915L17.6929 14.5106L17.6929 14.5297L17.6929 14.5487L17.6929 14.5676L17.6929 14.5865L17.6929 14.6053L17.6929 14.624L17.6929 14.6427L17.6929 14.6613L17.6929 14.6798L17.6929 14.6982L17.6929 14.7166L17.6929 14.7348L17.6929 14.7531L17.6929 14.7712L17.6929 14.7892L17.6929 14.8072L17.6929 14.8251L17.6929 14.843L17.6929 14.8607L17.6929 14.8784L17.6929 14.896L17.6929 14.9135L17.6929 14.9309L17.6929 14.9482L17.6929 14.9655L17.6929 14.9826L17.6929 14.9997L17.6929 15.0167L17.6929 15.0336L17.6929 15.0505L17.6929 15.0672L17.6929 15.0838L17.6929 15.1004L17.6929 15.1169L17.6929 15.1333L17.6929 15.1495L17.6929 15.1657L17.6929 15.1818L17.6929 15.1979L17.6929 15.2138L17.6929 15.2296L17.6929 15.2453L17.6929 15.261L17.6929 15.2765L17.6929 15.2919L17.6929 15.3073L17.6929 15.3225L17.6929 15.3377L17.6929 15.3527L17.6929 15.3676L17.6929 15.3825L17.6929 15.3972L17.6929 15.4119L17.6929 15.4264L17.6929 15.4408L17.6929 15.4552L17.6929 15.4694L17.6929 15.4835L17.6929 15.4975L17.6929 15.5114L17.6929 15.5252L17.6929 15.5389L17.6929 15.5524L17.6929 15.5659L17.6929 15.5792L17.6929 15.5925L17.6929 15.6056L17.6929 15.6186L17.6929 15.6315L17.6929 15.6443L17.6929 15.657L17.6929 15.6695L17.6929 15.682L17.6929 15.6943L17.6929 15.7065L17.6929 15.7186L17.6929 15.7306L17.6929 15.7424L17.6929 15.7541L17.6929 15.7658L17.6929 15.7772L17.6929 15.7886L17.6929 15.7998L17.6929 15.811L17.6929 15.8219L17.6929 15.8328L17.6929 15.8436L17.6929 15.8542L17.6929 15.8647L17.6929 15.875L17.6929 15.8852L17.6929 15.8953L17.6929 15.9053L17.6929 15.9152L17.6929 15.9249L17.6929 15.9344L17.6929 15.9439L17.6929 15.9532L17.6929 15.9624L17.6929 15.9714L17.6929 15.9803L17.6929 15.9891L17.6929 15.9977L17.6929 16.0062L17.6929 16.0146L17.6929 16.0228L17.6929 16.0309L17.6929 16.0388L17.6929 16.0466L17.6929 16.0543L17.6929 16.0618L17.6929 16.0692L17.6929 16.0764L17.6929 16.0835L17.6929 16.0904L17.6929 16.0972L17.6929 16.1015C17.6634 16.1611 17.6266 16.215 17.5891 16.2502C17.2903 16.5304 16.939 16.8365 16.5527 17.1731C16.5287 17.194 16.5046 17.215 16.4803 17.2362C16.0701 17.5936 15.6279 17.9804 15.1995 18.3827C14.3577 19.173 13.499 20.0858 13.0238 21.0424L13.0237 21.0425C12.7214 21.6512 12.4656 22.2891 12.2643 22.9385C12.2033 23.1353 12.1609 23.1903 12.1485 23.2035L12.1483 23.2037C12.146 23.2062 12.1435 23.2088 12.1332 23.2138C12.1201 23.2202 12.0857 23.2341 12.0155 23.2449L12.0155 23.2449C11.8142 23.2758 11.5543 23.1564 11.4217 22.9406L8.35732 11.0235ZM11.5595 20.4791L11.5595 20.4791C11.1589 21.4376 11.0264 22.2961 11.4216 22.9404L11.5595 20.4791ZM11.5595 20.4791C11.7615 19.996 12.0141 19.5336 12.2925 19.0341L12.2925 19.034M11.5595 20.4791L12.2925 19.034M12.2925 19.034C12.3249 18.9759 12.4892 18.6974 12.6552 18.4171M12.2925 19.034L12.6552 18.4171M12.6552 18.4171L12.8727 18.0503M12.6552 18.4171L12.8727 18.0503M12.8727 18.0503L12.9427 17.9325M12.8727 18.0503L12.9427 17.9325M12.9427 17.9325L12.9622 17.8998M12.9427 17.9325L12.9622 17.8998M12.9622 17.8998L12.9672 17.8912M12.9622 17.8998L12.9672 17.8912M12.9672 17.8912L12.9685 17.889M12.9672 17.8912L12.9685 17.889M12.9685 17.889L12.9689 17.8885M12.9685 17.889L12.9689 17.8885M12.9689 17.8885L12.9689 17.8884M12.9689 17.8885L12.9689 17.8884M12.9689 17.8884L12.969 17.8883M12.9689 17.8884L12.969 17.8883M12.1295 23.9862L12.1295 23.9861L12.1295 23.9862Z"
                                                                                        stroke="#181C32"
                                                                                        stroke-width="1.5" />
                                                                                </g>
                                                                            </svg>
                                                                            <span>
                                                                                <t
                                                                                    t-esc="idea.count_dislike()" />
                                                                            </span>
                                                                        </a>
                                                                    </div>
                                                                    <div>
                                                                        <span
                                                                            class="badge badge-status py-1">
                                                                            <t
                                                                                t-esc="idea.status.capitalize()" />
                                                                        </span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </t>
                                    </t>
                                </t>
                                <t t-if="idea_count == 0">
                                    <div class="text-center" id="no-idea-div">
                                        <hr />
                                        <h4>No Idea submitted yet.</h4>
                                        <hr />
                                    </div>
                                </t>
                            </div>

                            <nav class="mt-4">
                                <!-- <ul class="pagination justify-content-center">
                                    <li class="page-item disabled prev">
                                        <a class="page-link">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                                <g opacity="0.6" clip-path="url(#clip0_463_516)">
                                                    <path
                                                        d="M3.76808 7.20792L10.6478 0.328385C11.0855 -0.109463 11.795 -0.109463 12.2325 0.328385C12.6699
                                0.765842 12.6699 1.47536 12.2325 1.91278L6.14493 8.00011L12.2323
                                14.0872C12.6697 14.5249 12.6697 15.2343 12.2323 15.6718C11.7948
                                16.1094 11.0853 16.1094 10.6477 15.6718L3.76791 8.79213C3.54918
                                8.5733 3.43994 8.28679 3.43994 8.00015C3.43994 7.71336 3.54939
                                7.42665 3.76808 7.20792Z"
                                                        fill="#181C32" />
                                                </g>
                                                <defs>
                                                    <clipPath id="clip0_463_516">
                                                        <rect width="16" height="16" fill="white" transform="translate(16 16) rotate(-180)" />
                                                    </clipPath>
                                                </defs>
                                            </svg>
                                        </a>
                                    </li>
                                    <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                    <li class="page-item"><a class="page-link" href="#">2</a></li>
                                    <li class="page-item"><a class="page-link" href="#">3</a></li>
                                    <li class="page-item"><a class="page-link" href="#">...</a></li>
                                    <li class="page-item"><a class="page-link" href="#">6</a></li>
                                    <li class="page-item"><a class="page-link" href="#">7</a></li>
                                    <li class="page-item"><a class="page-link" href="#">8</a></li>
                                    <li class="page-item next">
                                        <a class="page-link" href="#">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                                <path
                                                    d="M12.2319 8.79208L5.35216 15.6716C4.91452 16.1095 4.20497 16.1095 3.76755 15.6716C3.33009 15.2342
                                3.33009 14.5246 3.76755 14.0872L9.85507 7.99989L3.76772
                                1.91276C3.33027 1.47513 3.33027 0.765684 3.76772 0.328226C4.20518
                                -0.109409 4.9147 -0.109409 5.35233 0.328226L12.2321 7.20786C12.4508
                                7.4267 12.5601 7.7132 12.5601 7.99985C12.5601 8.28664 12.4506
                                8.57335 12.2319 8.79208Z"
                                                    fill="#181C32" />
                                            </svg>
                                        </a>
                                    </li>
                                </ul> -->
                            </nav>
                        </div>
                    </section>
                </div>
            </t>
        </t>
    </template>
</odoo>