$(document).ready(function () {
    console.log('problem details doc ready');

    $('.idea-card-div').on('click', function () {
        var href = $(this).data('href');
        window.location.href = href;
    });

    $('.phase').on('click', function () {
        const phase = $(this);
        const phase_id = $(this).attr('phase-id');
        console.log('phase_id', phase_id);
        const phase_serial = $(this).attr('phase-serial');
        //console.log('odoo.csrf_token', odoo.csrf_token);

        $.ajax({
            type: 'post',
            url: '/problems/get-ideas-by-phase',
            // headers: {
            //     'X-CSRFToken': odoo.csrf_token
            // },
            contentType: 'application/json',
            data: JSON.stringify({ params: { phase_id: phase_id } }),
            success: function (response) {
                console.log('Response:', response);
                // Handle the response data here
                $('.phase').removeClass('active');
                phase.addClass('active');
                $('.phase-number').text(phase_serial + '.');
                $('.phase-name').text(response.result.phase.phase_name);
                $('.phase-timeline').text(response.result.phase.phase_start_date + ' - ' + response.result.phase.phase_end_date);
                $('.phase-description').text(response.result.phase.phase_description);
                $('#idea-count').text('Submitted Idea (' + response.result.ideas.length + ')');

                $('.idea-container').empty();
                if (response.result.ideas.length == 0) {
                    $('.idea-container').append(`
                        <div class="text-center" id="no-idea-div">
                            <hr/>
                            <h4>No Idea submitted yet.</h4>
                            <hr/>
                        </div>
                    `);
                }
                response.result.ideas.forEach(idea => {
                    let cover_photo = idea.idea_cover_photo == "" ? '/cep_idea/static/src/img/placeholder.png' : 'data:image/png;base64,' + idea.idea_cover_photo;
                    $('.idea-container').append(`
                    <div class="col-12 col-md-6 mt-4">
                        <div class="card card-idea">
                            <div class="card-body">
                                <div class="d-flex flex-column flex-md-row gap-3">
                                    <img class="featured-image" src="${cover_photo}" alt="..."/>

                                    <div class="d-flex flex-column gap-1">
                                        <div class="d-flex flex-row align-items-center gap-1">
                                            <svg width="50" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <rect width="50" height="50" rx="25" fill="#F2F2F2"/>
                                                <path d="M33.6668 34.75V32.5833C33.6668 31.4341 33.2103 30.3319 32.3976 29.5192C31.585 28.7065 30.4828 28.25 29.3335 28.25H20.6668C19.5176 28.25 18.4154 28.7065 17.6027 29.5192C16.79 30.3319 16.3335 31.4341 16.3335 32.5833V34.75" stroke="#1A2942" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M24.9998 23.9167C27.3931 23.9167 29.3332 21.9766 29.3332 19.5833C29.3332 17.1901 27.3931 15.25 24.9998 15.25C22.6066 15.25 20.6665 17.1901 20.6665 19.5833C20.6665 21.9766 22.6066 23.9167 24.9998 23.9167Z" stroke="#1A2942" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                                
                                            <span>${idea.idea_created_by} - ${idea.idea_creation_date}</span>
                                        </div>
                                        <a class="link-idea text-decoration-underline" href="/ideas/${idea.idea_id}/view">${idea.idea_title.substring(0, 48)}...</a>
                                        <p>${idea.idea_description}...</p>
                                        
                                        <div class="d-flex flex-column flex-md-row align-items-md-center justify-content-md-between gap-3">
                                            <div class="d-flex flex-row align-items-center gap-2">
                                                <a href="#" class="d-flex flex-row align-items-center gap-2 text-decoration-none">
                                                    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <rect width="32" height="32" rx="16" fill="#EEF3FE" />
                                                        <g opacity="0.8">
                                                            <path
                                                                d="M9.51306 23.9999C9.37609 23.9999 9.24727 23.9461 9.1504 23.8485C9.05347 23.7508 9 23.621 9 23.4828V15.898C9 15.6128 9.2304 15.3808 9.51347 15.3808L12.4909 15.3806C12.6282 15.3806 12.7572 15.4345 12.8541 15.5322C12.951 15.6297 13.0045 15.7598 13.0045 15.898V23.4829C13.0046 23.621 12.9511 23.7509 12.854 23.8487C12.757 23.9463 12.6281 24 12.4909 24L9.51306 23.9999Z"
                                                                stroke="#181C32" stroke-width="1.5" />
                                                            <path
                                                                d="M14.6009 23.9763C14.2783 23.9763 14.0051 23.7985 13.929 23.744C13.8393 23.6799 13.6581 23.5159 13.5566 23.4226C13.5566 23.4226 13.5566 15.7835 13.5566 15.758C13.5566 15.758 13.6472 15.4374 13.8973 15.2028C15.2159 13.9659 17.4813 12.2801 18.3041 10.6239C18.5887 10.0508 18.8295 9.45015 19.0189 8.83931C19.1624 8.3767 19.3533 8.09328 19.87 8.01382C20.4077 7.93113 20.9477 8.22836 21.2171 8.6672C21.8173 9.64537 21.5461 10.8196 21.1319 11.8101C20.9117 12.3369 20.6397 12.8331 20.3621 13.331C20.2848 13.4698 19.6752 14.495 19.6752 14.495C21.0617 14.4991 22.9846 14.5047 22.9846 14.5047C23.6821 14.5105 24.9994 14.8784 24.9996 16.2296C24.9996 16.8899 24.6191 17.2808 24.3056 17.494C24.6055 17.7325 24.952 18.1436 24.8703 18.7918C24.7915 19.4182 24.4697 19.774 24.1069 19.9408C24.3169 20.1895 24.4706 20.5684 24.382 21.0998C24.2984 21.6011 23.9783 21.8896 23.6657 22.0402C23.8248 22.2534 23.9342 22.5665 23.855 22.9897C23.6859 23.8936 22.9031 23.9466 22.5688 23.9692C22.539 23.9712 22.5115 23.973 22.4868 23.9753L22.476 23.9763H14.6009V23.9763Z"
                                                                stroke="#181C32" stroke-width="1.5" />
                                                        </g>
                                                    </svg>
                                                    <span>${idea.idea_likes}</span>
                                                </a>
                                                <a href="#" class="d-flex flex-row align-items-center gap-2 text-decoration-none">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none">
                                                        <rect width="30" height="30" rx="15" fill="#F2F2F2"/>
                                                        <g opacity="0.6">
                                                        <path d="M22.25 8.75012L22.25 15.8692L19.7455 15.8694L19.7455 8.74999L22.25 8.75012Z" stroke="#181C32" stroke-width="1.5"/>
                                                        <path d="M12.3243 17.505L12.969 17.8883L13.6406 16.7588L12.3265 16.755L10.1519 16.7486L9.33921 16.7462L9.10243 16.7455L9.03903 16.7453L9.02265 16.7453L9.01926 16.7453C8.77073 16.7429 8.41389 16.6712 8.1448 16.5022C7.91259 16.3564 7.74998 16.15 7.74989 15.7704L12.3243 17.505ZM12.3243 17.505L12.969 17.8883M12.3243 17.505L12.969 17.8883M8.35732 11.0235C8.32978 11.1888 8.34347 11.3051 8.36555 11.3843C8.38789 11.4644 8.42437 11.5264 8.46561 11.5753L9.10181 12.3287L8.2059 12.7406C8.13375 12.7738 8.06742 12.8242 8.01274 12.9C7.95837 12.9754 7.89882 13.0992 7.8733 13.3019L7.87329 13.302C7.85286 13.4641 7.88532 13.5736 7.93024 13.6578C7.98073 13.7525 8.06139 13.84 8.16069 13.9189L8.9583 14.5531L8.11573 15.1261C7.91737 15.261 7.74993 15.4505 7.74989 15.7702L8.35732 11.0235ZM8.35732 11.0235C8.37631 10.9097 8.41774 10.8358 8.46314 10.782C8.51229 10.7237 8.5795 10.674 8.65927 10.6356L9.47911 10.2407L8.935 9.51137C8.91199 9.48053 8.89164 9.4417 8.87965 9.39106C8.86786 9.34124 8.86017 9.26325 8.88167 9.14823C8.90434 9.02708 8.94054 8.96705 8.96311 8.93786C8.98634 8.90782 9.01635 8.8832 9.06053 8.86115C9.16883 8.80711 9.30674 8.7909 9.48132 8.77909L9.48158 8.77907C9.50176 8.7777 9.52884 8.77592 9.55627 8.77372L17.3986 8.77372L17.4022 8.77372C17.4297 8.77446 17.4732 8.78343 17.5284 8.80741C17.5839 8.8315 17.6245 8.85906 17.6338 8.86572L17.6342 8.866C17.6338 8.86576 17.637 8.86809 17.6445 8.87396C17.6521 8.87989 17.6618 8.88774 17.6737 8.89764C17.6798 8.90269 17.6862 8.90804 17.6929 8.91366L17.6929 8.91557L17.6929 8.92556L17.6929 8.93567L17.6929 8.94592L17.6929 8.9563L17.6929 8.96681L17.6929 8.97744L17.6929 8.9882L17.6929 8.99908L17.6929 9.01009L17.6929 9.02122L17.6929 9.03248L17.6929 9.04386L17.6929 9.05536L17.6929 9.06699L17.6929 9.07873L17.6929 9.0906L17.6929 9.10258L17.6929 9.11469L17.6929 9.12691L17.6929 9.13925L17.6929 9.15171L17.6929 9.16428L17.6929 9.17697L17.6929 9.18977L17.6929 9.20269L17.6929 9.21572L17.6929 9.22887L17.6929 9.24212L17.6929 9.25549L17.6929 9.26897L17.6929 9.28256L17.6929 9.29625L17.6929 9.31006L17.6929 9.32397L17.6929 9.338L17.6929 9.35212L17.6929 9.36636L17.6929 9.3807L17.6929 9.39514L17.6929 9.40969L17.6929 9.42434L17.6929 9.4391L17.6929 9.45395L17.6929 9.46891L17.6929 9.48397L17.6929 9.49912L17.6929 9.51438L17.6929 9.52974L17.6929 9.54519L17.6929 9.56074L17.6929 9.57639L17.6929 9.59213L17.6929 9.60797L17.6929 9.6239L17.6929 9.63992L17.6929 9.65604L17.6929 9.67225L17.6929 9.68855L17.6929 9.70495L17.6929 9.72143L17.6929 9.738L17.6929 9.75467L17.6929 9.77142L17.6929 9.78826L17.6929 9.80518L17.6929 9.82219L17.6929 9.83929L17.6929 9.85647L17.6929 9.87374L17.6929 9.89108L17.6929 9.90852L17.6929 9.92603L17.6929 9.94363L17.6929 9.9613L17.6929 9.97906L17.6929 9.99689L17.6929 10.0148L17.6929 10.0328L17.6929 10.0509L17.6929 10.069L17.6929 10.0872L17.6929 10.1055L17.6929 10.1239L17.6929 10.1424L17.6929 10.1609L17.6929 10.1795L17.6929 10.1981L17.6929 10.2169L17.6929 10.2357L17.6929 10.2545L17.6929 10.2735L17.6929 10.2925L17.6929 10.3116L17.6929 10.3307L17.6929 10.3499L17.6929 10.3692L17.6929 10.3886L17.6929 10.408L17.6929 10.4275L17.6929 10.447L17.6929 10.4666L17.6929 10.4863L17.6929 10.506L17.6929 10.5258L17.6929 10.5456L17.6929 10.5655L17.6929 10.5855L17.6929 10.6055L17.6929 10.6255L17.6929 10.6457L17.6929 10.6659L17.6929 10.6861L17.6929 10.7064L17.6929 10.7267L17.6929 10.7471L17.6929 10.7676L17.6929 10.7881L17.6929 10.8086L17.6929 10.8293L17.6929 10.8499L17.6929 10.8706L17.6929 10.8914L17.6929 10.9122L17.6929 10.933L17.6929 10.9539L17.6929 10.9748L17.6929 10.9958L17.6929 11.0168L17.6929 11.0379L17.6929 11.059L17.6929 11.0802L17.6929 11.1014L17.6929 11.1226L17.6929 11.1439L17.6929 11.1652L17.6929 11.1865L17.6929 11.2079L17.6929 11.2293L17.6929 11.2508L17.6929 11.2723L17.6929 11.2938L17.6929 11.3154L17.6929 11.337L17.6929 11.3586L17.6929 11.3803L17.6929 11.402L17.6929 11.4237L17.6929 11.4455L17.6929 11.4673L17.6929 11.4891L17.6929 11.5109L17.6929 11.5328L17.6929 11.5547L17.6929 11.5766L17.6929 11.5986L17.6929 11.6206L17.6929 11.6426L17.6929 11.6646L17.6929 11.6866L17.6929 11.7087L17.6929 11.7308L17.6929 11.7529L17.6929 11.7751L17.6929 11.7972L17.6929 11.8194L17.6929 11.8416L17.6929 11.8638L17.6929 11.886L17.6929 11.9082L17.6929 11.9305L17.6929 11.9528L17.6929 11.975L17.6929 11.9973L17.6929 12.0196L17.6929 12.042L17.6929 12.0643L17.6929 12.0866L17.6929 12.109L17.6929 12.1313L17.6929 12.1537L17.6929 12.1761L17.6929 12.1985L17.6929 12.2209L17.6929 12.2433L17.6929 12.2657L17.6929 12.2881L17.6929 12.3105L17.6929 12.3329L17.6929 12.3553L17.6929 12.3777L17.6929 12.4001L17.6929 12.4226L17.6929 12.445L17.6929 12.4674L17.6929 12.4898L17.6929 12.5122L17.6929 12.5346L17.6929 12.557L17.6929 12.5794L17.6929 12.6018L17.6929 12.6242L17.6929 12.6466L17.6929 12.669L17.6929 12.6913L17.6929 12.7137L17.6929 12.736L17.6929 12.7584L17.6929 12.7807L17.6929 12.803L17.6929 12.8253L17.6929 12.8476L17.6929 12.8699L17.6929 12.8922L17.6929 12.9144L17.6929 12.9367L17.6929 12.9589L17.6929 12.9811L17.6929 13.0033L17.6929 13.0254L17.6929 13.0476L17.6929 13.0697L17.6929 13.0918L17.6929 13.1139L17.6929 13.136L17.6929 13.158L17.6929 13.18L17.6929 13.202L17.6929 13.224L17.6929 13.246L17.6929 13.2679L17.6929 13.2898L17.6929 13.3117L17.6929 13.3335L17.6929 13.3553L17.6929 13.3771L17.6929 13.3989L17.6929 13.4206L17.6929 13.4423L17.6929 13.4639L17.6929 13.4856L17.6929 13.5072L17.6929 13.5287L17.6929 13.5503L17.6929 13.5717L17.6929 13.5932L17.6929 13.6146L17.6929 13.636L17.6929 13.6574L17.6929 13.6787L17.6929 13.6999L17.6929 13.7212L17.6929 13.7423L17.6929 13.7635L17.6929 13.7846L17.6929 13.8057L17.6929 13.8267L17.6929 13.8476L17.6929 13.8686L17.6929 13.8895L17.6929 13.9103L17.6929 13.9311L17.6929 13.9518L17.6929 13.9725L17.6929 13.9932L17.6929 14.0138L17.6929 14.0343L17.6929 14.0548L17.6929 14.0753L17.6929 14.0957L17.6929 14.116L17.6929 14.1363L17.6929 14.1565L17.6929 14.1767L17.6929 14.1968L17.6929 14.2169L17.6929 14.2369L17.6929 14.2568L17.6929 14.2767L17.6929 14.2966L17.6929 14.3163L17.6929 14.336L17.6929 14.3557L17.6929 14.3753L17.6929 14.3948L17.6929 14.4143L17.6929 14.4337L17.6929 14.453L17.6929 14.4723L17.6929 14.4915L17.6929 14.5106L17.6929 14.5297L17.6929 14.5487L17.6929 14.5676L17.6929 14.5865L17.6929 14.6053L17.6929 14.624L17.6929 14.6427L17.6929 14.6613L17.6929 14.6798L17.6929 14.6982L17.6929 14.7166L17.6929 14.7348L17.6929 14.7531L17.6929 14.7712L17.6929 14.7892L17.6929 14.8072L17.6929 14.8251L17.6929 14.843L17.6929 14.8607L17.6929 14.8784L17.6929 14.896L17.6929 14.9135L17.6929 14.9309L17.6929 14.9482L17.6929 14.9655L17.6929 14.9826L17.6929 14.9997L17.6929 15.0167L17.6929 15.0336L17.6929 15.0505L17.6929 15.0672L17.6929 15.0838L17.6929 15.1004L17.6929 15.1169L17.6929 15.1333L17.6929 15.1495L17.6929 15.1657L17.6929 15.1818L17.6929 15.1979L17.6929 15.2138L17.6929 15.2296L17.6929 15.2453L17.6929 15.261L17.6929 15.2765L17.6929 15.2919L17.6929 15.3073L17.6929 15.3225L17.6929 15.3377L17.6929 15.3527L17.6929 15.3676L17.6929 15.3825L17.6929 15.3972L17.6929 15.4119L17.6929 15.4264L17.6929 15.4408L17.6929 15.4552L17.6929 15.4694L17.6929 15.4835L17.6929 15.4975L17.6929 15.5114L17.6929 15.5252L17.6929 15.5389L17.6929 15.5524L17.6929 15.5659L17.6929 15.5792L17.6929 15.5925L17.6929 15.6056L17.6929 15.6186L17.6929 15.6315L17.6929 15.6443L17.6929 15.657L17.6929 15.6695L17.6929 15.682L17.6929 15.6943L17.6929 15.7065L17.6929 15.7186L17.6929 15.7306L17.6929 15.7424L17.6929 15.7541L17.6929 15.7658L17.6929 15.7772L17.6929 15.7886L17.6929 15.7998L17.6929 15.811L17.6929 15.8219L17.6929 15.8328L17.6929 15.8436L17.6929 15.8542L17.6929 15.8647L17.6929 15.875L17.6929 15.8852L17.6929 15.8953L17.6929 15.9053L17.6929 15.9152L17.6929 15.9249L17.6929 15.9344L17.6929 15.9439L17.6929 15.9532L17.6929 15.9624L17.6929 15.9714L17.6929 15.9803L17.6929 15.9891L17.6929 15.9977L17.6929 16.0062L17.6929 16.0146L17.6929 16.0228L17.6929 16.0309L17.6929 16.0388L17.6929 16.0466L17.6929 16.0543L17.6929 16.0618L17.6929 16.0692L17.6929 16.0764L17.6929 16.0835L17.6929 16.0904L17.6929 16.0972L17.6929 16.1015C17.6634 16.1611 17.6266 16.215 17.5891 16.2502C17.2903 16.5304 16.939 16.8365 16.5527 17.1731C16.5287 17.194 16.5046 17.215 16.4803 17.2362C16.0701 17.5936 15.6279 17.9804 15.1995 18.3827C14.3577 19.173 13.499 20.0858 13.0238 21.0424L13.0237 21.0425C12.7214 21.6512 12.4656 22.2891 12.2643 22.9385C12.2033 23.1353 12.1609 23.1903 12.1485 23.2035L12.1483 23.2037C12.146 23.2062 12.1435 23.2088 12.1332 23.2138C12.1201 23.2202 12.0857 23.2341 12.0155 23.2449L12.0155 23.2449C11.8142 23.2758 11.5543 23.1564 11.4217 22.9406L8.35732 11.0235ZM11.5595 20.4791L11.5595 20.4791C11.1589 21.4376 11.0264 22.2961 11.4216 22.9404L11.5595 20.4791ZM11.5595 20.4791C11.7615 19.996 12.0141 19.5336 12.2925 19.0341L12.2925 19.034M11.5595 20.4791L12.2925 19.034M12.2925 19.034C12.3249 18.9759 12.4892 18.6974 12.6552 18.4171M12.2925 19.034L12.6552 18.4171M12.6552 18.4171L12.8727 18.0503M12.6552 18.4171L12.8727 18.0503M12.8727 18.0503L12.9427 17.9325M12.8727 18.0503L12.9427 17.9325M12.9427 17.9325L12.9622 17.8998M12.9427 17.9325L12.9622 17.8998M12.9622 17.8998L12.9672 17.8912M12.9622 17.8998L12.9672 17.8912M12.9672 17.8912L12.9685 17.889M12.9672 17.8912L12.9685 17.889M12.9685 17.889L12.9689 17.8885M12.9685 17.889L12.9689 17.8885M12.9689 17.8885L12.9689 17.8884M12.9689 17.8885L12.9689 17.8884M12.9689 17.8884L12.969 17.8883M12.9689 17.8884L12.969 17.8883M12.1295 23.9862L12.1295 23.9861L12.1295 23.9862Z" stroke="#181C32" stroke-width="1.5"/>
                                                        </g>
                                                    </svg>
                                                    <span>${idea.idea_dislikes}</span>
                                                </a>
                                            </div>
                                            <div>
                                                <span class="badge badge-status py-1">${idea.idea_status}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    `);
                });
            },
            error: function (xhr, status, error) {
                console.error('Error:', error);
                // Handle errors
            }
        });
    });

    //search
    $('#search-btn').on('click', function (e) {
        const keyword = $('#search-text').val();
        console.log(keyword);
        const phase_id = $('.active').attr('phase-id');
        console.log(phase_id);
        $.ajax({
            type: 'post',
            url: '/problems/ideas/search',
            contentType: 'application/json',
            data: JSON.stringify({ params: { phase_id: phase_id, keyword: keyword } }),
            success: function (response) {
                console.log('Response:', response);
                $('.idea-container').empty();
                response.result.ideas.forEach(idea => {
                    let cover_photo = idea.idea_cover_photo == "" ? '/cep_idea/static/src/img/placeholder.png' : 'data:image/png;base64,' + idea.idea_cover_photo;
                    $('.idea-container').append(`
                    <div class="col-12 col-md-6 mt-4">
                        <div class="card card-idea">
                            <div class="card-body">
                                <div class="d-flex flex-column flex-md-row gap-3">
                                    <img class="featured-image" src="${cover_photo}" alt="..."/>

                                    <div class="d-flex flex-column gap-1">
                                        <div class="d-flex flex-row align-items-center gap-1">
                                            <svg width="50" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <rect width="50" height="50" rx="25" fill="#F2F2F2"/>
                                                <path d="M33.6668 34.75V32.5833C33.6668 31.4341 33.2103 30.3319 32.3976 29.5192C31.585 28.7065 30.4828 28.25 29.3335 28.25H20.6668C19.5176 28.25 18.4154 28.7065 17.6027 29.5192C16.79 30.3319 16.3335 31.4341 16.3335 32.5833V34.75" stroke="#1A2942" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M24.9998 23.9167C27.3931 23.9167 29.3332 21.9766 29.3332 19.5833C29.3332 17.1901 27.3931 15.25 24.9998 15.25C22.6066 15.25 20.6665 17.1901 20.6665 19.5833C20.6665 21.9766 22.6066 23.9167 24.9998 23.9167Z" stroke="#1A2942" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                                
                                            <span>${idea.idea_created_by} - ${idea.idea_creation_date}</span>
                                        </div>
                                        <a class="link-idea text-decoration-underline" href="/ideas/${idea.idea_id}/view">${idea.idea_title.substring(0, 48)}...</a>
                                        <p>${idea.idea_description}...</p>
                                        
                                        <div class="d-flex flex-column flex-md-row align-items-md-center justify-content-md-between gap-3">
                                            <div class="d-flex flex-row align-items-center gap-2">
                                                <a href="#" class="d-flex flex-row align-items-center gap-2 text-decoration-none">
                                                    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <rect width="32" height="32" rx="16" fill="#EEF3FE" />
                                                        <g opacity="0.8">
                                                            <path
                                                                d="M9.51306 23.9999C9.37609 23.9999 9.24727 23.9461 9.1504 23.8485C9.05347 23.7508 9 23.621 9 23.4828V15.898C9 15.6128 9.2304 15.3808 9.51347 15.3808L12.4909 15.3806C12.6282 15.3806 12.7572 15.4345 12.8541 15.5322C12.951 15.6297 13.0045 15.7598 13.0045 15.898V23.4829C13.0046 23.621 12.9511 23.7509 12.854 23.8487C12.757 23.9463 12.6281 24 12.4909 24L9.51306 23.9999Z"
                                                                stroke="#181C32" stroke-width="1.5" />
                                                            <path
                                                                d="M14.6009 23.9763C14.2783 23.9763 14.0051 23.7985 13.929 23.744C13.8393 23.6799 13.6581 23.5159 13.5566 23.4226C13.5566 23.4226 13.5566 15.7835 13.5566 15.758C13.5566 15.758 13.6472 15.4374 13.8973 15.2028C15.2159 13.9659 17.4813 12.2801 18.3041 10.6239C18.5887 10.0508 18.8295 9.45015 19.0189 8.83931C19.1624 8.3767 19.3533 8.09328 19.87 8.01382C20.4077 7.93113 20.9477 8.22836 21.2171 8.6672C21.8173 9.64537 21.5461 10.8196 21.1319 11.8101C20.9117 12.3369 20.6397 12.8331 20.3621 13.331C20.2848 13.4698 19.6752 14.495 19.6752 14.495C21.0617 14.4991 22.9846 14.5047 22.9846 14.5047C23.6821 14.5105 24.9994 14.8784 24.9996 16.2296C24.9996 16.8899 24.6191 17.2808 24.3056 17.494C24.6055 17.7325 24.952 18.1436 24.8703 18.7918C24.7915 19.4182 24.4697 19.774 24.1069 19.9408C24.3169 20.1895 24.4706 20.5684 24.382 21.0998C24.2984 21.6011 23.9783 21.8896 23.6657 22.0402C23.8248 22.2534 23.9342 22.5665 23.855 22.9897C23.6859 23.8936 22.9031 23.9466 22.5688 23.9692C22.539 23.9712 22.5115 23.973 22.4868 23.9753L22.476 23.9763H14.6009V23.9763Z"
                                                                stroke="#181C32" stroke-width="1.5" />
                                                        </g>
                                                    </svg>
                                                    <span>${idea.idea_likes}</span>
                                                </a>
                                                <a href="#" class="d-flex flex-row align-items-center gap-2 text-decoration-none">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none">
                                                        <rect width="30" height="30" rx="15" fill="#F2F2F2"/>
                                                        <g opacity="0.6">
                                                        <path d="M22.25 8.75012L22.25 15.8692L19.7455 15.8694L19.7455 8.74999L22.25 8.75012Z" stroke="#181C32" stroke-width="1.5"/>
                                                        <path d="M12.3243 17.505L12.969 17.8883L13.6406 16.7588L12.3265 16.755L10.1519 16.7486L9.33921 16.7462L9.10243 16.7455L9.03903 16.7453L9.02265 16.7453L9.01926 16.7453C8.77073 16.7429 8.41389 16.6712 8.1448 16.5022C7.91259 16.3564 7.74998 16.15 7.74989 15.7704L12.3243 17.505ZM12.3243 17.505L12.969 17.8883M12.3243 17.505L12.969 17.8883M8.35732 11.0235C8.32978 11.1888 8.34347 11.3051 8.36555 11.3843C8.38789 11.4644 8.42437 11.5264 8.46561 11.5753L9.10181 12.3287L8.2059 12.7406C8.13375 12.7738 8.06742 12.8242 8.01274 12.9C7.95837 12.9754 7.89882 13.0992 7.8733 13.3019L7.87329 13.302C7.85286 13.4641 7.88532 13.5736 7.93024 13.6578C7.98073 13.7525 8.06139 13.84 8.16069 13.9189L8.9583 14.5531L8.11573 15.1261C7.91737 15.261 7.74993 15.4505 7.74989 15.7702L8.35732 11.0235ZM8.35732 11.0235C8.37631 10.9097 8.41774 10.8358 8.46314 10.782C8.51229 10.7237 8.5795 10.674 8.65927 10.6356L9.47911 10.2407L8.935 9.51137C8.91199 9.48053 8.89164 9.4417 8.87965 9.39106C8.86786 9.34124 8.86017 9.26325 8.88167 9.14823C8.90434 9.02708 8.94054 8.96705 8.96311 8.93786C8.98634 8.90782 9.01635 8.8832 9.06053 8.86115C9.16883 8.80711 9.30674 8.7909 9.48132 8.77909L9.48158 8.77907C9.50176 8.7777 9.52884 8.77592 9.55627 8.77372L17.3986 8.77372L17.4022 8.77372C17.4297 8.77446 17.4732 8.78343 17.5284 8.80741C17.5839 8.8315 17.6245 8.85906 17.6338 8.86572L17.6342 8.866C17.6338 8.86576 17.637 8.86809 17.6445 8.87396C17.6521 8.87989 17.6618 8.88774 17.6737 8.89764C17.6798 8.90269 17.6862 8.90804 17.6929 8.91366L17.6929 8.91557L17.6929 8.92556L17.6929 8.93567L17.6929 8.94592L17.6929 8.9563L17.6929 8.96681L17.6929 8.97744L17.6929 8.9882L17.6929 8.99908L17.6929 9.01009L17.6929 9.02122L17.6929 9.03248L17.6929 9.04386L17.6929 9.05536L17.6929 9.06699L17.6929 9.07873L17.6929 9.0906L17.6929 9.10258L17.6929 9.11469L17.6929 9.12691L17.6929 9.13925L17.6929 9.15171L17.6929 9.16428L17.6929 9.17697L17.6929 9.18977L17.6929 9.20269L17.6929 9.21572L17.6929 9.22887L17.6929 9.24212L17.6929 9.25549L17.6929 9.26897L17.6929 9.28256L17.6929 9.29625L17.6929 9.31006L17.6929 9.32397L17.6929 9.338L17.6929 9.35212L17.6929 9.36636L17.6929 9.3807L17.6929 9.39514L17.6929 9.40969L17.6929 9.42434L17.6929 9.4391L17.6929 9.45395L17.6929 9.46891L17.6929 9.48397L17.6929 9.49912L17.6929 9.51438L17.6929 9.52974L17.6929 9.54519L17.6929 9.56074L17.6929 9.57639L17.6929 9.59213L17.6929 9.60797L17.6929 9.6239L17.6929 9.63992L17.6929 9.65604L17.6929 9.67225L17.6929 9.68855L17.6929 9.70495L17.6929 9.72143L17.6929 9.738L17.6929 9.75467L17.6929 9.77142L17.6929 9.78826L17.6929 9.80518L17.6929 9.82219L17.6929 9.83929L17.6929 9.85647L17.6929 9.87374L17.6929 9.89108L17.6929 9.90852L17.6929 9.92603L17.6929 9.94363L17.6929 9.9613L17.6929 9.97906L17.6929 9.99689L17.6929 10.0148L17.6929 10.0328L17.6929 10.0509L17.6929 10.069L17.6929 10.0872L17.6929 10.1055L17.6929 10.1239L17.6929 10.1424L17.6929 10.1609L17.6929 10.1795L17.6929 10.1981L17.6929 10.2169L17.6929 10.2357L17.6929 10.2545L17.6929 10.2735L17.6929 10.2925L17.6929 10.3116L17.6929 10.3307L17.6929 10.3499L17.6929 10.3692L17.6929 10.3886L17.6929 10.408L17.6929 10.4275L17.6929 10.447L17.6929 10.4666L17.6929 10.4863L17.6929 10.506L17.6929 10.5258L17.6929 10.5456L17.6929 10.5655L17.6929 10.5855L17.6929 10.6055L17.6929 10.6255L17.6929 10.6457L17.6929 10.6659L17.6929 10.6861L17.6929 10.7064L17.6929 10.7267L17.6929 10.7471L17.6929 10.7676L17.6929 10.7881L17.6929 10.8086L17.6929 10.8293L17.6929 10.8499L17.6929 10.8706L17.6929 10.8914L17.6929 10.9122L17.6929 10.933L17.6929 10.9539L17.6929 10.9748L17.6929 10.9958L17.6929 11.0168L17.6929 11.0379L17.6929 11.059L17.6929 11.0802L17.6929 11.1014L17.6929 11.1226L17.6929 11.1439L17.6929 11.1652L17.6929 11.1865L17.6929 11.2079L17.6929 11.2293L17.6929 11.2508L17.6929 11.2723L17.6929 11.2938L17.6929 11.3154L17.6929 11.337L17.6929 11.3586L17.6929 11.3803L17.6929 11.402L17.6929 11.4237L17.6929 11.4455L17.6929 11.4673L17.6929 11.4891L17.6929 11.5109L17.6929 11.5328L17.6929 11.5547L17.6929 11.5766L17.6929 11.5986L17.6929 11.6206L17.6929 11.6426L17.6929 11.6646L17.6929 11.6866L17.6929 11.7087L17.6929 11.7308L17.6929 11.7529L17.6929 11.7751L17.6929 11.7972L17.6929 11.8194L17.6929 11.8416L17.6929 11.8638L17.6929 11.886L17.6929 11.9082L17.6929 11.9305L17.6929 11.9528L17.6929 11.975L17.6929 11.9973L17.6929 12.0196L17.6929 12.042L17.6929 12.0643L17.6929 12.0866L17.6929 12.109L17.6929 12.1313L17.6929 12.1537L17.6929 12.1761L17.6929 12.1985L17.6929 12.2209L17.6929 12.2433L17.6929 12.2657L17.6929 12.2881L17.6929 12.3105L17.6929 12.3329L17.6929 12.3553L17.6929 12.3777L17.6929 12.4001L17.6929 12.4226L17.6929 12.445L17.6929 12.4674L17.6929 12.4898L17.6929 12.5122L17.6929 12.5346L17.6929 12.557L17.6929 12.5794L17.6929 12.6018L17.6929 12.6242L17.6929 12.6466L17.6929 12.669L17.6929 12.6913L17.6929 12.7137L17.6929 12.736L17.6929 12.7584L17.6929 12.7807L17.6929 12.803L17.6929 12.8253L17.6929 12.8476L17.6929 12.8699L17.6929 12.8922L17.6929 12.9144L17.6929 12.9367L17.6929 12.9589L17.6929 12.9811L17.6929 13.0033L17.6929 13.0254L17.6929 13.0476L17.6929 13.0697L17.6929 13.0918L17.6929 13.1139L17.6929 13.136L17.6929 13.158L17.6929 13.18L17.6929 13.202L17.6929 13.224L17.6929 13.246L17.6929 13.2679L17.6929 13.2898L17.6929 13.3117L17.6929 13.3335L17.6929 13.3553L17.6929 13.3771L17.6929 13.3989L17.6929 13.4206L17.6929 13.4423L17.6929 13.4639L17.6929 13.4856L17.6929 13.5072L17.6929 13.5287L17.6929 13.5503L17.6929 13.5717L17.6929 13.5932L17.6929 13.6146L17.6929 13.636L17.6929 13.6574L17.6929 13.6787L17.6929 13.6999L17.6929 13.7212L17.6929 13.7423L17.6929 13.7635L17.6929 13.7846L17.6929 13.8057L17.6929 13.8267L17.6929 13.8476L17.6929 13.8686L17.6929 13.8895L17.6929 13.9103L17.6929 13.9311L17.6929 13.9518L17.6929 13.9725L17.6929 13.9932L17.6929 14.0138L17.6929 14.0343L17.6929 14.0548L17.6929 14.0753L17.6929 14.0957L17.6929 14.116L17.6929 14.1363L17.6929 14.1565L17.6929 14.1767L17.6929 14.1968L17.6929 14.2169L17.6929 14.2369L17.6929 14.2568L17.6929 14.2767L17.6929 14.2966L17.6929 14.3163L17.6929 14.336L17.6929 14.3557L17.6929 14.3753L17.6929 14.3948L17.6929 14.4143L17.6929 14.4337L17.6929 14.453L17.6929 14.4723L17.6929 14.4915L17.6929 14.5106L17.6929 14.5297L17.6929 14.5487L17.6929 14.5676L17.6929 14.5865L17.6929 14.6053L17.6929 14.624L17.6929 14.6427L17.6929 14.6613L17.6929 14.6798L17.6929 14.6982L17.6929 14.7166L17.6929 14.7348L17.6929 14.7531L17.6929 14.7712L17.6929 14.7892L17.6929 14.8072L17.6929 14.8251L17.6929 14.843L17.6929 14.8607L17.6929 14.8784L17.6929 14.896L17.6929 14.9135L17.6929 14.9309L17.6929 14.9482L17.6929 14.9655L17.6929 14.9826L17.6929 14.9997L17.6929 15.0167L17.6929 15.0336L17.6929 15.0505L17.6929 15.0672L17.6929 15.0838L17.6929 15.1004L17.6929 15.1169L17.6929 15.1333L17.6929 15.1495L17.6929 15.1657L17.6929 15.1818L17.6929 15.1979L17.6929 15.2138L17.6929 15.2296L17.6929 15.2453L17.6929 15.261L17.6929 15.2765L17.6929 15.2919L17.6929 15.3073L17.6929 15.3225L17.6929 15.3377L17.6929 15.3527L17.6929 15.3676L17.6929 15.3825L17.6929 15.3972L17.6929 15.4119L17.6929 15.4264L17.6929 15.4408L17.6929 15.4552L17.6929 15.4694L17.6929 15.4835L17.6929 15.4975L17.6929 15.5114L17.6929 15.5252L17.6929 15.5389L17.6929 15.5524L17.6929 15.5659L17.6929 15.5792L17.6929 15.5925L17.6929 15.6056L17.6929 15.6186L17.6929 15.6315L17.6929 15.6443L17.6929 15.657L17.6929 15.6695L17.6929 15.682L17.6929 15.6943L17.6929 15.7065L17.6929 15.7186L17.6929 15.7306L17.6929 15.7424L17.6929 15.7541L17.6929 15.7658L17.6929 15.7772L17.6929 15.7886L17.6929 15.7998L17.6929 15.811L17.6929 15.8219L17.6929 15.8328L17.6929 15.8436L17.6929 15.8542L17.6929 15.8647L17.6929 15.875L17.6929 15.8852L17.6929 15.8953L17.6929 15.9053L17.6929 15.9152L17.6929 15.9249L17.6929 15.9344L17.6929 15.9439L17.6929 15.9532L17.6929 15.9624L17.6929 15.9714L17.6929 15.9803L17.6929 15.9891L17.6929 15.9977L17.6929 16.0062L17.6929 16.0146L17.6929 16.0228L17.6929 16.0309L17.6929 16.0388L17.6929 16.0466L17.6929 16.0543L17.6929 16.0618L17.6929 16.0692L17.6929 16.0764L17.6929 16.0835L17.6929 16.0904L17.6929 16.0972L17.6929 16.1015C17.6634 16.1611 17.6266 16.215 17.5891 16.2502C17.2903 16.5304 16.939 16.8365 16.5527 17.1731C16.5287 17.194 16.5046 17.215 16.4803 17.2362C16.0701 17.5936 15.6279 17.9804 15.1995 18.3827C14.3577 19.173 13.499 20.0858 13.0238 21.0424L13.0237 21.0425C12.7214 21.6512 12.4656 22.2891 12.2643 22.9385C12.2033 23.1353 12.1609 23.1903 12.1485 23.2035L12.1483 23.2037C12.146 23.2062 12.1435 23.2088 12.1332 23.2138C12.1201 23.2202 12.0857 23.2341 12.0155 23.2449L12.0155 23.2449C11.8142 23.2758 11.5543 23.1564 11.4217 22.9406L8.35732 11.0235ZM11.5595 20.4791L11.5595 20.4791C11.1589 21.4376 11.0264 22.2961 11.4216 22.9404L11.5595 20.4791ZM11.5595 20.4791C11.7615 19.996 12.0141 19.5336 12.2925 19.0341L12.2925 19.034M11.5595 20.4791L12.2925 19.034M12.2925 19.034C12.3249 18.9759 12.4892 18.6974 12.6552 18.4171M12.2925 19.034L12.6552 18.4171M12.6552 18.4171L12.8727 18.0503M12.6552 18.4171L12.8727 18.0503M12.8727 18.0503L12.9427 17.9325M12.8727 18.0503L12.9427 17.9325M12.9427 17.9325L12.9622 17.8998M12.9427 17.9325L12.9622 17.8998M12.9622 17.8998L12.9672 17.8912M12.9622 17.8998L12.9672 17.8912M12.9672 17.8912L12.9685 17.889M12.9672 17.8912L12.9685 17.889M12.9685 17.889L12.9689 17.8885M12.9685 17.889L12.9689 17.8885M12.9689 17.8885L12.9689 17.8884M12.9689 17.8885L12.9689 17.8884M12.9689 17.8884L12.969 17.8883M12.9689 17.8884L12.969 17.8883M12.1295 23.9862L12.1295 23.9861L12.1295 23.9862Z" stroke="#181C32" stroke-width="1.5"/>
                                                        </g>
                                                    </svg>
                                                    <span>${idea.idea_dislikes}</span>
                                                </a>
                                            </div>
                                            <div>
                                                <span class="badge badge-status py-1">${idea.idea_status}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    `);
                });
                $('#search-text').val('');
            },
            error: function (xhr, status, error) {
                console.error('Error:', error);
                // Handle errors
            }
        });
    });

});