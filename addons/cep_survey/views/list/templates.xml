<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="cep_survey.list" name="Survey List" inherit_id="website.layout">
        <xpath expr="//main" position="inside">
            <t t-set="head">
                <t t-call-assets="web.assets_common" />
                <t t-call-assets="web.assets_frontend" />
                <t t-call-assets="cep_survey.survey_list" />
            </t>
            <t t-if=" data != undefined ">
                <br />
                <div class="container">
                    <div class="row">
                        <t t-foreach="data" t-as="survey">
                            <div class="col-xl-4 col-xxl-4">
                                <!--begin::Mixed
                                Widget 6-->
                                <div class="card card-custom gutter-b card-stretch">
                                    <!--begin::Header-->
                                    <t t-if="survey.background_image_url">
                                        <img t-att-src="survey.background_image_url"
                                            class="card-img-top featured-image"
                                            t-att-alt="survey.title" />
                                    </t>
                                    <t t-else="">
                                        <img src="/cep_survey/static/src/img/placeholder.png"
                                            class="card-img-top featured-image" alt="cover" />
                                    </t>
                                    <div class="card-img-overlay">
                                        <div
                                            class="card-toolbar d-none d-md-block d-lg-block d-xl-block">
                                            <div class="float-right" data-placement="left">
                                                <a
                                                    t-att-href="'/survey/start/' + survey.access_token"
                                                    class="btn btn-primary rounded-pill">
                                                    <i class="fa fa-clipboard"></i> Take Survey </a>
                                            </div>
                                        </div>
                                    </div>
                                    <!--end::Header-->
                                    <!--begin::Body-->
                                    <div class="card-body">
                                        <!--begin::Container-->
                                        <div class="pt-1">
                                            <!--begin::Title-->
                                            <h3
                                                class="font-size-h4 text-dark-75 text-hover-primary font-weight-bold pt-8">
                                                <t t-esc="survey.title" />
                                            </h3>
                                            <!--end::Title-->
                                            <!--begin::Text-->
                                            <!-- <p class="text-dark-75 font-size-lg
                                            font-weight-normal pt-3 mb-4"><t
                                            t-esc="survey.description[:100]"/></p> -->
                                            <!--end::Text-->
                                            <div class="d-none">
                                                <p>
                                                    <span class="font-weight-bolder">3 months
                                                        remaining</span>
                                                </p>
                                                <div class="progress" style="height: 5px;">
                                                    <div
                                                        class="progress-bar progress-bar-striped progress-bar-animated bg-primary"
                                                        role="progressbar" style="width: 60%"
                                                        aria-valuenow="50" aria-valuemin="0"
                                                        aria-valuemax="100"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <!--end::Container-->
                                    </div>
                                    <!--end::Body-->
                                    <!--begin::Footer-->
                                    <div class="card-footer">
                                        <div
                                            class="d-flex align-items-center justify-content-between flex-wrap py-4">
                                            <div class="symbol-group symbol-hover py-1 mr-2">
                                                <div
                                                    class="symbol symbol-35 symbol-circle symbol-light-primary d-flex gap-2">
                                                    <span class="symbol-label font-weight-bolder">+<t
                                                            t-esc="survey.answer_count" /></span>
                                                    <p class="mt-1">Participants</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div
                                            class="d-sm-flex d-md-none d-lg-none d-xl-none align-items-center justify-content-between flex-wrap py-4">
                                            <a t-att-href="'/survey/start/' + survey.access_token"
                                                class="btn btn-primary rounded-pill">
                                                <i class="fa fa-clipboard"></i> Take Survey </a>
                                        </div>
                                    </div>
                                    <!--end::Footer-->
                                </div>
                                <!--end::Mixed
                                Widget 6-->
                            </div>
                        </t>
                    </div>
                </div>
                <br />
            </t>
        </xpath>
    </template>
</odoo>