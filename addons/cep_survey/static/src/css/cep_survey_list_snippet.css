.s_surveys .btn-filter {
  border-radius: 5px;
  border: 1px solid #d8e2ef;
}

.s_surveys .card {
  border-radius: 16px;
  background: #fff;
  box-shadow: 0px 8px 30px 0px rgba(24, 28, 50, 0.16);
  height: 330px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.s_surveys .card-survey .featured-image {
  max-width: 100%;
  height: 100px;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  display: block;
  margin: 8px;
  margin-bottom: 4px;
}

.s_surveys .card-survey .card-body {
  flex: 1;
  padding: 1.25rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.s_surveys .card-survey .link {
  color: rgba(13, 12, 34, 0.9);
  font-family: Nunito;
  font-size: 18px;
  font-weight: 400;
  line-height: normal;
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 100%;
}

.s_surveys .pagination .page-item {
  margin-right: 4px;
  margin-left: 4px;

  border-radius: 4px;
  border: 1px solid #d8e2ef;
  background: #fff;
}

.s_surveys .pagination .page-item.prev,
.s_surveys .pagination .page-item.next {
  border-radius: 0;
  border: none;
  background: none;
}

.s_surveys .dropdown-toggle {
  color: #181c32;
  font-family: 'Nunito';
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}

.s_surveys .search.input-group {
  border-radius: 5px;
  border-top-right-radius: 5px !important;
  border-bottom-right-radius: 5px !important;
  border: 1px solid #d8e2ef;
  background: rgba(255, 255, 255, 0.5);
  padding: 2px !important;
}

.s_surveys .search .form-control,
.s_surveys .search .input-group-append {
  border-radius: 0px;
  border: none;
  background: rgba(255, 255, 255, 0.5);
}

.s_surveys .pagination .page-item .page-link {
  border-radius: 4px;
  border: 1px solid #d8e2ef;
  background: #fff;

  color: #181c32;
  text-align: center;
  font-family: 'Nunito';
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.s_surveys .pagination .page-item.active .page-link {
  background: #c9d6f1;
}

.s_surveys .pagination .page-item.prev .page-link,
.s_surveys .pagination .page-item.next .page-link {
  border: 0;
  border-radius: 0;
  background: none;
}

/* CSS by Atiq */
.participants {
  color: #1fccc6;
}

.link {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* CSS by Tanvir */
.s_surveys #suggestion-list .dropdown-item {
  white-space: normal;
}

.logo-container {
  width: 40px;
  height: 40px;
}

.logo-container .logo-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 2px solid #1fccc6;
}