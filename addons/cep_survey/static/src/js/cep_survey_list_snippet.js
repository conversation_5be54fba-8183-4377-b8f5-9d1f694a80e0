/** @odoo-module **/

import publicWidget from 'web.public.widget';

const per_page = 8;
const page_per_slot = 5; //(1-5,6-10,...)

publicWidget.registry.surveyList = publicWidget.Widget.extend({
  selector: '.s_surveys',

  start() {
    console.log('survey list snippet ready');
    console.log(this.el.querySelector('#surveys'));
    let surveyList = this.el.querySelector('#surveys');
    this._rpc({
      route: `/survey-list/1/${per_page}`,
      params: {},
    }).then((data) => {
      console.log(data);
      $('.survey-count').text(data.survey_count);
      let html = createSurveyCard(data.survey_list);
      surveyList.innerHTML = html;
      // pagination code
      $('.pagination').empty();
      const total_pages =
        data.total_pages <= page_per_slot ? data.total_pages : page_per_slot;
      if (total_pages <= 0) return;
      let lis = `
                <li class="page-item prev" data-page-no="1">
                    <a class="page-link" href="#">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <g opacity="0.6" clip-path="url(#clip0_463_516)">
                                <path
                                    d="M3.76808 7.20792L10.6478 0.328385C11.0855 -0.109463 11.795 -0.109463 12.2325 0.328385C12.6699 0.765842 12.6699 1.47536 12.2325 1.91278L6.14493 8.00011L12.2323 14.0872C12.6697 14.5249 12.6697 15.2343 12.2323 15.6718C11.7948 16.1094 11.0853 16.1094 10.6477 15.6718L3.76791 8.79213C3.54918 8.5733 3.43994 8.28679 3.43994 8.00015C3.43994 7.71336 3.54939 7.42665 3.76808 7.20792Z"
                                    fill="#181C32" />
                            </g>
                            <defs>
                                <clipPath id="clip0_463_516">
                                    <rect width="16" height="16" fill="white" transform="translate(16 16) rotate(-180)" />
                                </clipPath>
                            </defs>
                        </svg>
                    </a>
                </li>
            `;
      for (let i = 1; i <= total_pages; i++) {
        lis += `<li class="page-item ${i == 1 ? 'active' : ''
          }" data-page-no="${i}"><a class="page-link" href="#">${i}</a></li>`;
      }
      lis += `
                <li class="page-item next" data-page-no="${total_pages > 1 ? 2 : 1
        }">
                    <a class="page-link" href="#">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path
                                d="M12.2319 8.79208L5.35216 15.6716C4.91452 16.1095 4.20497 16.1095 3.76755 15.6716C3.33009 15.2342 3.33009 14.5246 3.76755 14.0872L9.85507 7.99989L3.76772 1.91276C3.33027 1.47513 3.33027 0.765684 3.76772 0.328226C4.20518 -0.109409 4.9147 -0.109409 5.35233 0.328226L12.2321 7.20786C12.4508 7.4267 12.5601 7.7132 12.5601 7.99985C12.5601 8.28664 12.4506 8.57335 12.2319 8.79208Z"
                                fill="#181C32" />
                        </svg>
                    </a>
                </li>
            `;
      $('.pagination').append(lis);
    });

    // Bind search button click event
    this.$('#search-btn').on('click', this._onSearchButtonClick.bind(this));

    // apply search when somthing enter pressed
    this.$('#search-text').on('keypress', (event) => {
      if (event.which === 13) {
        this._onSearchButtonClick(event);
      }
    });

    // Bind Pagination click event
    this.$('.pagination').on(
      'click',
      '.page-item',
      this._getSurveysByPage.bind(this)
    );

    // Bind search suggestions related events
    this.$('#search-text').on('input', this._onSearchInput.bind(this));
  },

  _getSurveysByPage(ev) {
    ev.preventDefault();
    let page_no = $(ev.currentTarget).data('page-no');
    console.log('page no: ' + page_no);
    this._rpc({
      route: `/survey-list/${page_no}/${per_page}`,
      params: {},
    }).then((data) => {
      console.log(data);
      let html = createSurveyCard(data.survey_list);
      let surveyList = this.el.querySelector('#surveys');
      surveyList.innerHTML = html;
      $('.survey-count').text(data.survey_count);
      // pagination code
      $('.pagination').empty();
      let total_pages = data.total_pages;
      if (total_pages <= 0) return;
      let slot_number = Math.ceil(page_no / page_per_slot);
      // Ensure that the slot number is within bounds
      slot_number =
        slot_number > Math.ceil(total_pages / page_per_slot)
          ? Math.ceil(total_pages / page_per_slot)
          : slot_number;
      console.log('slot number: ' + slot_number);
      // Calculate the start and end page number for the slot
      let start_page = (slot_number - 1) * page_per_slot + 1;
      let end_page = slot_number * page_per_slot;
      end_page = end_page >= total_pages ? total_pages : end_page;
      console.log('start page: ' + start_page);
      console.log('end page: ' + end_page);
      let lis = `
                <li class="page-item prev" data-page-no="${page_no == 1 ? 1 : page_no - 1
        }">
                    <a class="page-link" href="#">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <g opacity="0.6" clip-path="url(#clip0_463_516)">
                                <path
                                    d="M3.76808 7.20792L10.6478 0.328385C11.0855 -0.109463 11.795 -0.109463 12.2325 0.328385C12.6699 0.765842 12.6699 1.47536 12.2325 1.91278L6.14493 8.00011L12.2323 14.0872C12.6697 14.5249 12.6697 15.2343 12.2323 15.6718C11.7948 16.1094 11.0853 16.1094 10.6477 15.6718L3.76791 8.79213C3.54918 8.5733 3.43994 8.28679 3.43994 8.00015C3.43994 7.71336 3.54939 7.42665 3.76808 7.20792Z"
                                    fill="#181C32" />
                            </g>
                            <defs>
                                <clipPath id="clip0_463_516">
                                    <rect width="16" height="16" fill="white" transform="translate(16 16) rotate(-180)" />
                                </clipPath>
                            </defs>
                        </svg>
                    </a>
                </li>
            `;
      for (let i = start_page; i <= end_page; i++) {
        lis += `<li class="page-item ${i == page_no ? 'active' : ''
          }" data-page-no="${i}"><a class="page-link" href="#">${i}</a></li>`;
      }
      lis += `
                <li class="page-item next" data-page-no="${page_no == total_pages ? total_pages : page_no + 1
        }">
                    <a class="page-link" href="#">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path
                                d="M12.2319 8.79208L5.35216 15.6716C4.91452 16.1095 4.20497 16.1095 3.76755 15.6716C3.33009 15.2342 3.33009 14.5246 3.76755 14.0872L9.85507 7.99989L3.76772 1.91276C3.33027 1.47513 3.33027 0.765684 3.76772 0.328226C4.20518 -0.109409 4.9147 -0.109409 5.35233 0.328226L12.2321 7.20786C12.4508 7.4267 12.5601 7.7132 12.5601 7.99985C12.5601 8.28664 12.4506 8.57335 12.2319 8.79208Z"
                                fill="#181C32" />
                        </svg>
                    </a>
                </li>
            `;
      $('.pagination').append(lis);
    });
  },

  _onSearchButtonClick(ev) {
    ev.preventDefault();
    console.log('Search Button clicked!');
    const keyword = $('#search-text').val();
    console.log(keyword);
    this._rpc({
      route: '/survey-search',
      params: {
        keyword: keyword,
      },
    }).then((data) => {
      console.log(data);
      let html = createSurveyCard(data.survey_list);
      let surveyList = this.el.querySelector('#surveys');
      surveyList.innerHTML = html;
      $('.survey-count').text(data.survey_count);
      // $('#search-text').val('');
      $('.pagination').empty();
    });
  },

  // search suggestions related methods
  _onSearchInput: function (event) {
    var keyword = this.$('#search-text').val().trim();
    if (keyword.length > 2) {
      this._fetchSuggestions(keyword);
    } else {
      this.$('#suggestion-list').hide().empty();
    }
  },

  _fetchSuggestions: function (keyword) {
    this._rpc({
      route: '/survey/search_suggestions',
      params: {
        keyword: keyword,
      },
    }).then((data) => {
      this._showSuggestions(data.surveys);
    });
  },

  _showSuggestions: function (surveys) {
    var $list = this.$('#suggestion-list');
    $list.empty().hide();

    if (surveys.length) {
      surveys.forEach((survey) => {
        $list.append(`
                        <li class="dropdown-item border-bottom">
                            <a href="/survey/start/${survey.survey_access_token}" target="_blank" class="text-decoration-none d-block w-full">
                                ${survey.survey_title}
                            </a>
                        </li>`);
      });
      $list.show();
    }
  },
});

function createSurveyCard(surveys) {
  let html = ``;
  if (surveys.length == 0)
    html += `<div class="col-12 text-center"><hr/><h4>No surveys submitted yet!</h4><hr/></div>`;
  surveys.forEach((survey) => {
    console.log(
      '\x1b[41m%s\x1b[0m',
      '📌 cep_survey_list_snippet-225-> survey =>',
      survey
    );
    let cover_photo =
      survey.survey_cover_photo == ''
        ? '/cep_survey/static/src/img/placeholder.png'
        : survey.survey_cover_photo;
    const survey_logo = survey.survey_logo
      ? `
           <img src="data:image/*;base64,${survey.survey_logo}" alt="Logo" class="logo-img" />
       `
      : '';

    html += `
            <div class="col-12 col-md-3 mt-4">
              <a href="/survey/start/${survey.survey_access_token}" class="text-decoration-none text-reset" style="display: block;">
                  <div class="card card-survey h-100">
                      <img class="featured-image" src="${cover_photo}" alt="..." />

                      <div class="card-body">
                          <div class="d-flex flex-column gap-3">
                              <div class="d-flex flex-column gap-3">
                                  <div class="d-flex flex-row align-items-center gap-1">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 26 26" fill="none">
                                          <circle cx="13.0007" cy="13" r="9.66667" stroke="#1FCCC6" stroke-width="2" />
                                          <path d="M17.875 13H13.25C13.1119 13 13 12.8881 13 12.75V9.20831" stroke="#1FCCC6" stroke-width="2" stroke-linecap="round" />
                                      </svg>

                                      <div class="d-flex flex-column gap-1">
                                          <span>${survey.survey_create_date}</span>
                                      </div>
                                  </div>

                                  <span class="link text-decoration-underline" title="${survey.survey_title}">${survey.survey_title}</span>

                                  <div class="d-flex flex-row align-items-center justify-content-between gap-1 participants">
                                      <div>   
                                          <i class="fa fa-users"></i>
                                          <span>Participants - ${survey.survey_participants_count}</span>
                                      </div>
                                      <div class="logo-container">
                                          ${survey_logo}
                                      </div>
                                  </div>
                              </div>
                          </div>
                      </div>
                  </div>
              </a>
          </div>
        `;
  });
  return html;
}

export default publicWidget.registry.surveyList;
