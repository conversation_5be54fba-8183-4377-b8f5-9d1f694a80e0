window.UIUtils = class UIUtils {
  static #getIcon(type) {
    switch (type) {
      case 'success':
        return '<i class="fa fa-check-circle" aria-hidden="true"></i> ';
      case 'danger':
        return '<i class="fa fa-times-circle" aria-hidden="true"></i> ';
      case 'warning':
        return '<i class="fa fa-exclamation-triangle" aria-hidden="true"></i> ';
      case 'info':
      default:
        return '<i class="fa fa-info-circle" aria-hidden="true"></i> ';
    }
  }

  static #showAlert({ message = '', type = 'info', dismissSecs = 5, withClose = true } = {}) {
    const alertId = 'alert-' + Date.now();
    const iconHtml = this.#getIcon(type);

    const alertHtml = `
      <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show shadow position-fixed start-50 translate-middle-x top-0 mt-3"
           style="z-index: 1060; min-width: 300px;" role="alert">
        ${iconHtml}${message}
        ${withClose ? '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' : ''}
      </div>
    `;

    const $alert = $(alertHtml).appendTo('body');

    if (dismissSecs > 0) {
      setTimeout(() => {
        $alert.alert('close');
      }, dismissSecs * 1000);
    }
  }

  static showSuccess(message, dismissSecs = 5, withClose = true) {
    this.#showAlert({ message, type: 'success', dismissSecs, withClose });
  }

  static showError(message, dismissSecs = 5, withClose = true) {
    this.#showAlert({ message, type: 'danger', dismissSecs, withClose });
  }

  static showInfo(message, dismissSecs = 5, withClose = true) {
    this.#showAlert({ message, type: 'info', dismissSecs, withClose });
  }

  static showWarning(message, dismissSecs = 5, withClose = true) {
    this.#showAlert({ message, type: 'warning', dismissSecs, withClose });
  }
};
