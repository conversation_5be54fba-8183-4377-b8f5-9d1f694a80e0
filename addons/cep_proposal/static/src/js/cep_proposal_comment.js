odoo.define('cep_idea.reaction', function (require) {
  "use strict";

  var publicWidget = require('web.public.widget');

  publicWidget.registry.ReactionButton = publicWidget.Widget.extend({
    selector: '.s_proposal_details',
    events: {
      'click .reaction_button': '_onReact',
      'click .btn.delete': '_onDelete',
    },

    /**
     * React to proposal (like/dislike)
     */
    _onReact: async function (ev) {
      ev.preventDefault();
      const $button = $(ev.currentTarget);
      const model = $button.data('model');
      const id = $button.data('id');
      const type = $button.data('type');

      try {
        const result = await this._rpc({
          route: `/${model}/${id}/reactions/${type}/create`,
          params: {},
        });

        if (result.error) {
          if (result.error === 'not_authenticated') {
            // Redirect to login if user not authenticated
            window.location.href = result.redirect_url || '/web/login';
            return;
          }
          alert("Something went wrong: " + (result.error || 'Unknown error'));
          return;
        }

        // Update counts on success
        $('#likes_count_' + model + '_' + id).text(result.likes_count);
        $('#dislikes_count_' + model + '_' + id).text(result.dislikes_count);

        // Update button classes
        const $likeBtn = $(`button.reaction_button[data-model="${model}"][data-id="${id}"][data-type="like"]`);
        const $dislikeBtn = $(`button.reaction_button[data-model="${model}"][data-id="${id}"][data-type="dislike"]`);

        if (type === "like") {
          $likeBtn.addClass('liked');
        } else {
          $likeBtn.removeClass('liked');
        }

        if (type === "dislike") {
          $dislikeBtn.addClass('disliked');
        } else {
          $dislikeBtn.removeClass('disliked');
        }
      } catch (err) {
        console.error("Unhandled _onReact error:", err);
        alert("An unexpected error occurred. Redirecting to login.");
        window.location.href = '/web/login';
      }
    },

    /**
     * Delete comment
     */
    _onDelete: async function (ev) {
      ev.preventDefault();
      const $button = $(ev.currentTarget);
      const commentId = $button.data('id');
      const typeOfComment = $button.data('type');

      try {
        const result = await this._rpc({
          route: `/proposal-comment/${commentId}/delete`,
          params: {},
        });

        if (result.error) {
          if (result.error === 'not_authenticated') {
            window.location.href = result.redirect_url || '/web/login';
            return;
          }
          alert("Something went wrong: " + (result.error || 'Unknown error'));
          return;
        }

        if (result.success) {
          $('#' + typeOfComment + '-' + commentId).remove();
        } else {
          alert("Failed to delete the comment.");
        }
      } catch (err) {
        console.error("Unhandled _onDelete error:", err);
        alert("An unexpected error occurred. Redirecting to login.");
        window.location.href = '/web/login';
      }
    },
  });
});
