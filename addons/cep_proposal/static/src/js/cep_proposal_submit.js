$(document).ready(function () {
    initTags();
    initBackButton();
    const quill = initRichTextEditor();
    initQuillCharacterHandler(quill);
    initCoverPhotoPreview();
    initAttachmentPreview();
    initFormSubmission(quill);
    initFileDeletion();
});

/* ----------------- TAG INPUT HANDLING ----------------- */
function initTags() {
    const tagContainer = $('#tagContainer');

    $('#tag-input').on('keypress', function (e) {
        if (e.which === 13) {
            e.preventDefault();
            const inputValue = $(this).val().trim();
            if (inputValue) {
                const tag = $('<div class="tag"></div>').text(inputValue);
                const closeBtn = $('<span class="close-btn">&times;</span>').click(function () {
                    $(this).parent().remove();
                });
                tag.append(closeBtn);
                tagContainer.append(tag);
                $(this).val('');
            }
        }
    });

    tagContainer.on('click', '.close-btn', function () {
        $(this).parent().remove();
    });
}

/* ----------------- BACK BUTTON ----------------- */
function initBackButton() {
    $('.back-btn').click(function () {
        history.back();
    });
}

/* ----------------- QUILL RICH TEXT EDITOR ----------------- */
function initRichTextEditor() {
    const quill = new Quill('#description_editor', {
        modules: { toolbar: true },
        placeholder: 'Write description here ...',
        theme: 'snow',
        formats: [
            'background', 'bold', 'color', 'font', 'code', 'italic', 'link', 'size',
            'strike', 'script', 'underline', 'blockquote', 'header', 'indent', 'list',
            'align', 'direction', 'code-block', 'formula'
        ]
    });
    return quill;
}

function initQuillCharacterHandler(quill) {
    const maxChars = 600;
    const counter = $('#description-char-numbers');

    const updateCharCount = () => {
        counter.text(quill.getLength() - 1);
    };

    const enforceLimit = () => {
        if (quill.getLength() > maxChars) {
            quill.deleteText(maxChars, quill.getLength());
        }
    };

    updateCharCount();
    enforceLimit();

    quill.on('text-change', () => {
        updateCharCount();
        enforceLimit();
    });
}

/* ----------------- IMAGE PREVIEW ----------------- */
function initCoverPhotoPreview() {
    $('#cover_photo').change(function () {
        const previewContainer = $('#cover_photo_preview');
        const file = this.files[0];

        if (!file) {
            // No file selected, clear preview
            previewContainer.empty();
            $('#delete_cover_photo_input').val('');
            return;
        }

        if (!isFileSizeValid(file, 10)) {
            UIUtils.showError('File size greater than 10MB not accepted!');
            $('#cover_photo').val('');
            return;
        }

        // Valid file: clear preview and show new image
        previewContainer.empty();

        const reader = new FileReader();
        reader.onload = function (e) {
            previewContainer.append(`
        <div class="position-relative">
          <img src="${e.target.result}" class="img-thumbnail" />
          <button type="button" class="remove-file-btn btn btn-sm btn-danger position-absolute top-0 start-100 translate-middle" data-file-type="cover_photo">&times;</button>
        </div>
      `);
        };
        reader.readAsDataURL(file);

        $('#delete_cover_photo_input').val('');
    });
}

/* ----------------- ATTACHMENT PREVIEW ----------------- */
function initAttachmentPreview() {
    $('#attachment').change(function () {
        const allowed = ['doc', 'docx', 'pdf', 'jpg', 'jpeg', 'png'];
        const maxSizeMB = 10;

        Array.from(this.files).forEach((file, i) => {
            const ext = file.name.split('.').pop().toLowerCase();

            if (!allowed.includes(ext)) {
                UIUtils.showError(`"${file.name}" is not a supported file type.`);
                return;
            }

            if (!isFileSizeValid(file, maxSizeMB)) {
                UIUtils.showError(`"${file.name}" exceeds the 10MB size limit.`);
                return;
            }

            const reader = new FileReader();
            reader.onload = function (e) {
                $('#attachment_preview').append(
                    `<div class="position-relative border rounded p-2">
                        <div class="d-flex flex-column align-items-start">
                            <p class="mb-1">${file.name}</p>
                            <p class="text-muted mb-1">${(file.size / 1024).toFixed(2)} KB</p>
                            <button type="button" class="btn btn-danger btn-sm position-absolute top-0 start-100 translate-middle remove-attachment-btn" data-file-type="attachment" title="Remove">&times;</button>
                        </div>
                    </div>`
                );
            };
            reader.readAsDataURL(file);
        });
    });
}


/* ----------------- File Deletion ----------------- */
function initFileDeletion() {
    $(document).on('click', '.remove-file-btn, .remove-attachment-btn', function () {
        const fileType = $(this).data('file-type');

        if (fileType === 'cover_photo') {
            $('#delete_cover_photo_input').val('yes');
            $('#cover_photo').val('');
            $('#cover_photo_preview').children().remove();
        } else if (fileType === 'attachment') {
            const attachmentId = $(this).data('attachment-id');
            let ids = JSON.parse($('#delete_attachment_ids').val() || '[]');
            if (!ids.includes(attachmentId)) {
                ids.push(attachmentId);
            }
            $('#delete_attachment_ids').val(JSON.stringify(ids));
            $(this).closest('.position-relative').remove();
        }
    });
}

/* ----------------- FORM SUBMISSION ----------------- */
function initFormSubmission(quill) {
    const form = $('#proposal_submit_form').length ? $('#proposal_submit_form') : $('#proposal_update_form');

    form.on('keypress change', function () {
        form.removeClass('was-validated');
        $('#min_votes, #end_date').each(function () {
            this.setCustomValidity('');
        });
    });

    form.on('submit', function (e) {
        let valid = true;

        const minVotesInput = $('#min_votes');
        const minVotes = Number(minVotesInput.val());
        const minVotesRequired = Number(minVotesInput.data('min'));

        if (minVotes < minVotesRequired) {
            minVotesInput.get(0).setCustomValidity(minVotesInput.closest('div').find('.invalid-feedback').text().trim());
            valid = false;
        }

        const endDateInput = $('#end_date');
        const endDate = new Date(endDateInput.val());
        const createDate = endDateInput.data('create_date');
        const startDate = createDate ? new Date(createDate.split(' ')[0]) : new Date();
        const requiredDays = Number(endDateInput.data('end_date_required'));

        const daysDiff = Math.floor((endDate - startDate) / (1000 * 60 * 60 * 24));
        if (daysDiff < requiredDays) {
            endDateInput.get(0).setCustomValidity(endDateInput.closest('div').find('.invalid-feedback').text().trim());
            valid = false;
        }

        if (!valid) {
            e.preventDefault();
            form.addClass('was-validated');
            return;
        }

        // Attach rich text and tags
        form.find('textarea[name="description"]').val(quill.getSemanticHTML());
        const tags = $('#tagContainer .tag').map(function () {
            return $(this).text().replace('×', '').trim();
        }).get();
        form.find('input[name="all_tags"]').val(JSON.stringify(tags));
    });
}

/* ----------------- UTILITY ----------------- */
function isFileSizeValid(file, maxSizeMB) {
    if (!file || typeof file.size === 'undefined') return false;
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    return file.size <= maxSizeBytes;
}