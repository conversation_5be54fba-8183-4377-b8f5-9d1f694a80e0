/** @odoo-module **/

import publicWidget from 'web.public.widget';

const per_page = 8;
const page_per_slot = 5;//(1-5,6-10,...)

publicWidget.registry.proposalList = publicWidget.Widget.extend({
    selector: '.s_proposals',

    start() {
        let proposalList = this.el.querySelector('#proposals');
        this._rpc({
            route: `/proposals/proposal-list/1/${per_page}`,
            params: {}
        }).then(data => {
            $('.proposal-count').text(data.proposal_count);
            let html = createProposalCard(data.proposals);
            proposalList.innerHTML = html;

            // pagination code
            $('.pagination').empty();
            const total_pages = data.total_pages <= page_per_slot ? data.total_pages : page_per_slot;
            if (total_pages <= 0) return;
            let lis = `
                <li class="page-item prev" data-page-no="1">
                    <a class="page-link" href="#">
                        <i class="fa fa-chevron-left" style="opacity: 0.6; color: #181C32;"></i>
                    </a>
                </li>
            `;
            for (let i = 1; i <= total_pages; i++) {
                lis += `<li class="page-item ${i == 1 ? 'active' : ''}" data-page-no="${i}"><a class="page-link" href="#">${i}</a></li>`;
            }
            lis += `
                <li class="page-item next" data-page-no="${total_pages > 1 ? 2 : 1}">
                    <a class="page-link" href="#">
                        <i class="fa fa-chevron-right" style="opacity: 0.6; color: #181C32;"></i>
                    </a>
                </li>
            `;
            $('.pagination').append(lis);
        });

        // Bind search button click event
        this.$('#search-btn').on('click', this._onSearchButtonClick.bind(this));

        // apply search when somthing enter pressed
        this.$('#search-text').on('keypress', (event) => {
            if (event.which === 13) {
                this._onSearchButtonClick(event)
            }
        })

        // Bind Pagination click event
        this.$('.pagination').on('click', '.page-item', this._getProposalsByPage.bind(this));

        // Bind search suggestions related events
        this.$('#search-text').on('input', this._onSearchInput.bind(this));
    },

    _getProposalsByPage(ev) {
        ev.preventDefault();
        let page_no = $(ev.currentTarget).data('page-no');
        this._rpc({
            route: `/proposals/proposal-list/${page_no}/${per_page}`,
            params: {}
        }).then(data => {
            let html = createProposalCard(data.proposals);
            let proposalList = this.el.querySelector('#proposals');
            proposalList.innerHTML = html;
            $('.proposal-count').text(data.proposal_count);
            // pagination code
            $('.pagination').empty();
            let total_pages = data.total_pages;
            if (total_pages <= 0) return;
            let slot_number = Math.ceil(page_no / page_per_slot);
            // Ensure that the slot number is within bounds
            slot_number = slot_number > Math.ceil(total_pages / page_per_slot) ? Math.ceil(total_pages / page_per_slot) : slot_number;
            // Calculate the start and end page number for the slot
            let start_page = (slot_number - 1) * page_per_slot + 1;
            let end_page = slot_number * page_per_slot;
            end_page = end_page >= total_pages ? total_pages : end_page;
            let lis = `
                <li class="page-item prev" data-page-no="${page_no == 1 ? 1 : (page_no - 1)}">
                    <a class="page-link" href="#">
                       <i class="fa fa-chevron-left" style="opacity: 0.6; color: #181C32;"></i>
                    </a>
                </li>
            `;
            for (let i = start_page; i <= end_page; i++) {
                lis += `<li class="page-item ${i == page_no ? 'active' : ''}" data-page-no="${i}"><a class="page-link" href="#">${i}</a></li>`;
            }
            lis += `
                <li class="page-item next" data-page-no="${page_no == total_pages ? total_pages : (page_no + 1)}">
                    <a class="page-link" href="#">
                        <i class="fa fa-chevron-right" style="opacity: 0.6; color: #181C32;"></i>
                    </a>
                </li>
            `;
            $('.pagination').append(lis);
        });
    },

    _onSearchButtonClick(ev) {
        ev.preventDefault();
        const keyword = $('#search-text').val();
        this._rpc({
            route: '/proposals/search',
            params: {
                keyword: keyword
            }
        }).then(data => {
            let html = createProposalCard(data.proposals);
            let proposalList = this.el.querySelector('#proposals');
            proposalList.innerHTML = html;
            $('.proposal-count').text(data.proposal_count);
            // $('#search-text').val('');
            $('.pagination').empty();
        });
    },

    // search suggestions related methods
    _onSearchInput: function (event) {
        var keyword = this.$('#search-text').val().trim();
        if (keyword.length > 2) {
            this._fetchSuggestions(keyword);
        } else {
            this.$('#suggestion-list').hide().empty();
        }
    },

    _fetchSuggestions: function (keyword) {
        this._rpc({
            route: '/proposals/search_suggestions',
            params: {
                keyword: keyword
            }
        }).then((data) => {
            this._showSuggestions(data.proposals)
        })
    },

    _showSuggestions: function (proposals) {
        var $list = this.$('#suggestion-list');
        $list.empty().hide();

        if (proposals.length) {
            proposals.forEach(proposal => {
                $list.append(`
                    <li class="dropdown-item border-bottom">
                        <a href="/proposals/${proposal.proposal_id}/view" target="_blank" class="text-decoration-none d-block w-full">
                            ${proposal.proposal_title}
                        </a>
                    </li>`
                );
            });
            $list.show();
        }
    },
});

function createProposalCard(proposals) {
    let html = ``;
    if (proposals.length == 0) html += `<div class="col-12 text-center"><hr/><h4>No proposals submitted yet!</h4><hr/></div>`;
    proposals.forEach(proposal => {
        let cover_photo = proposal.proposal_cover_photo == "" ? '/cep_proposal/static/src/img/placeholder.png' : 'data:image/png;base64,' + proposal.proposal_cover_photo;
        html += `
            <div class="col-12 col-md-3 mt-4">
                <div class="card card-proposal">
                    <img class="featured-image" src="${cover_photo}" alt="..."/>

                    <div class="card-body">
                        <div class="d-flex flex-column gap-3">
                            <div class="d-flex flex-column gap-3">
                                <div class="d-flex align-items-center gap-2">
                                    <i class="fa fa-clock-o participants" aria-hidden="true" style="font-size: 22px"></i>
                                    <div class="d-flex flex-column gap-1">
                                        <span style="font-size: 14px">${proposal.proposal_created_date} - ${proposal.proposal_end_date}</span>
                                    </div>
                                </div>
                                <a class="link  title="${proposal.proposal_title}" href="/proposals/${proposal.proposal_id}/view">${proposal.proposal_title.substring(0, 47)}...</a>
                                <div class="d-flex flex-row align-items-center gap-1 participants">
                                    <i class="fa fa-users"></i>
                                    <span>Votes - ${proposal.proposal_votes}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    return html;
};

export default publicWidget.registry.proposalList;
