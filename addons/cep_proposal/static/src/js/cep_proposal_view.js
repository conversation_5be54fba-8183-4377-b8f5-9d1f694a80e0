$(document).ready(function () {
    const currentURL = window.location.href;

    // -----------------------------
    // Sharing Functionality
    // -----------------------------

    console.log("Current URL:", currentURL);

    $('#shareFacebook').on('click', function () {
        const url = 'https://www.facebook.com/sharer/sharer.php?u=' + currentURL;
        window.open(url, '_blank');
    });

    $('#shareTwitter').on('click', function () {
        const url = 'https://twitter.com/intent/tweet?url=' + currentURL;
        window.open(url, '_blank');
    });

    $('#shareLinkedIn').on('click', function () {
        const url = 'https://www.linkedin.com/shareArticle?mini=true&url=' + currentURL;
        window.open(url, '_blank');
    });

    $('#shareWhatsApp').on('click', function () {
        const url = 'https://wa.me/?text=' + currentURL;
        window.open(url, '_blank');
    });

    $('#copyLink').on('click', function () {
        const $temp = $('<input>');
        $('body').append($temp);
        $temp.val(currentURL).select();
        document.execCommand('copy');
        $temp.remove();
        alert('Link copied to clipboard');
    });

    // -----------------------------
    // Form Confirmation Modal
    // -----------------------------

    const $form = $('form[action="official-update/create"]');
    let isConfirmed = false;

    // Intercept form submission to show modal first
    $form.on('submit', function (e) {
        if (!isConfirmed) {
            e.preventDefault();
            $('#confirmationModal').modal('show');
        }
    });

    // On confirm button click inside modal, submit the form
    $('#confirmPublish').on('click', function () {
        isConfirmed = true;
        $('#confirmationModal').modal('hide');
        $form.submit();
    });
});
