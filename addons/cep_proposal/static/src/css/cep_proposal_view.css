.s_proposal_details .author {
    color: #181C32;
    font-family: 'Nuni<PERSON>';
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
}

.s_proposal_details .date {
    color: #181C32;
    font-family: 'Nuni<PERSON>';
    font-size: 24px;
    font-style: normal;
    font-weight: 500;
}

.s_proposal_details .location {
    color: #181C32;
    font-family: Nunito;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

.s_proposal_details .featured-image {
    height: 300px;
    border-radius: 16px;
    background-repeat: no-repeat;
    background-size: cover;
}

.s_proposal_details .card {
    border-radius: 16px;
    background: #FFF;
    box-shadow: 0px 12px 40px 0px rgba(9, 23, 107, 0.12);
}

.s_proposal_details .card-header {
    border-radius: 16px 16px 0px 0px;
    background: #09176B;

    color: #FFF;
    font-family: 'Nunito';
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
}

.s_proposal_details .summary {
    color: #181C32;
    font-family: <PERSON>uni<PERSON>;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

.s_proposal_details .status {
    color: #181C32;
    font-family: Nunito;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

.s_proposal_details .dropdown-toggle {
    border-radius: 5px;
    border: 1px solid #D8E2EF;
    background: #FFF;

    color: #181C32;
    font-family: 'Nunito';
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
}

.s_proposal_details .btn-cancel {
    border-radius: 5px;
    border: 1px solid #D8E2EF;
    background: #FFF;
    flex-shrink: 0;

    color: #181C32;
    font-family: 'Nunito';
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
}

.s_proposal_details .btn-post {
    border-radius: 5px;
    background: #1FCCC6;
    flex-shrink: 0;

    color: #FFF;
    font-family: 'Nunito';
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
}

.s_proposal_details .form-control {
    background: #FFF;
    color: #181C32;
    font-family: 'Nunito';
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
}

.s_proposal_details .form-floating label {
    color: #181C32;
    font-family: 'Nunito';
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
}

.s_proposal_details .reaction-on-comment {
    border-top: 1px solid #D8E2EF;
    border-bottom: 1px solid #D8E2EF;
    margin-bottom: 40px;
}

.s_proposal_details .official-update textarea {
    border-radius: 8px;
    border: 1px solid #ACB7C6;
    background: #FFF;
    height: 100% !important;
}

.s_proposal_details .official-update .date {
    color: #181C32;
    font-family: 'Nunito';
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    opacity: 0.8;
}

.s_proposal_details .progress {
    border-radius: 20px;
}

.s_proposal_details .progress-bar {
    background-color: #1FCCC6;
    border-radius: 20px;
}