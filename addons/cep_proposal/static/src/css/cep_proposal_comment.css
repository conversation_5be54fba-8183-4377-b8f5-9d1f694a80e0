:root {
  --idea-thumb-size: 25px;
  --thumb-bg-color: #EEF3FE;
  --comment-thumb-size: 15px;
}

.s_proposal_details .reaction-on-comment .reaction_button .fa-thumbs-o-up {
  background-color: var(--thumb-bg-color);
  font-size: var(--comment-thumb-size);
}

.s_proposal_details .reaction-on-comment .reaction_button .rounded-circle {
  width: 30px;
  height: 30px;
  background-color: var(--thumb-bg-color);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
}

.s_proposal_details .reaction-on-comment .reaction_button .fa-thumbs-o-down {
  font-size: var(--comment-thumb-size);
}

.s_proposal_details .fa-trash-o {
  color: #181C32;
  opacity: 0.7;
  font-size: 18px;
}

.reaction_button.liked {
  color: #0d6efd;
  font-weight: 800;
}

.reaction_button.disliked {
  color: #dc3545;
  font-weight: 800;
}