from odoo import http
from odoo.http import request
import json
import base64
import logging
from datetime import datetime

_logger = logging.getLogger(__name__)


def isAuthenticate():
    return request.env.user.id != request.website.user_id


def isProposalOwner(owner_id):
    return owner_id.id == request.env.user.id


class ProposalController(http.Controller):

    @http.route('/proposals', type='http', auth='user', website=True)
    def list(self, **kwargs):
        proposals = request.env['cep.proposal.proposal'].sudo().search([])
        context = {'proposals': proposals}
        return request.render('cep_proposal.proposals_list', context)

    @http.route(['/proposals/new'], type='http', auth='user', website=True)
    def new(self, **kwargs):
        if not isAuthenticate():
            return request.redirect('/web/login')

        return request.render('cep_proposal.proposals_new', {
            'min_votes': 150,
            'min_votes_required': 150,
            'min_votes_message': 'A proposal minimum votes must have at least 150 votes.',
            'end_date_required': 30,
            'end_date_message': 'A proposal end date must be at least 30 days after the create date.',
        })

    @http.route(['/proposals/create'], type='http', auth='user', website=True)
    def create(self, **kwargs):
        if not isAuthenticate():
            return request.redirect('/web/login')
        kwargs['owner_id'] = request.env.user.id
        kwargs['cover_photo'] = base64.b64encode(kwargs['cover_photo'].read())
        kwargs['all_tags'] = kwargs['all_tags']
        tags = json.loads(kwargs['all_tags'])

        attachments = []
        if 'attachment' in kwargs and kwargs['attachment']:
            attachments = request.httprequest.files.getlist('attachment')

        del kwargs['attachment']

        proposal = request.env['cep.proposal.proposal'].sudo().create(kwargs)

        for tag in tags:
            proposal_tag = request.env['cep.proposal.tag'].sudo().create({
                'name': tag,
                'owner_id': request.env.user.id
            })
            proposal.write({'tag_ids': [(4, proposal_tag.id)]})

        # # Handling the attachments
        attachment_ids = []
        if len(attachments) > 0:
            for attachment in attachments:
                attachment_data = {
                    'name': attachment.filename,
                    'datas': base64.b64encode(attachment.read()),
                    'res_model': 'cep.proposal.proposal',
                    'res_id': proposal.id,
                }
                attachment_id = request.env['ir.attachment'].sudo().create(
                    attachment_data)
                attachment_ids.append(attachment_id.id)

        if attachment_ids:
            proposal.write({'attachment_ids': [(6, 0, attachment_ids)]})

        return request.redirect('/proposals/{}/view'.format(proposal.id))

    @http.route(['/proposals/edit/<string:id>'], type='http', auth='user', website=True)
    def edit(self, id, **kwargs):
        if not isAuthenticate():
            return request.redirect('/web/login')
        proposal = request.env['cep.proposal.proposal'].browse(int(id)).sudo()
        if not isProposalOwner(proposal.owner_id):
            return request.redirect('/my/proposals')
        if not proposal.exists():
            return request.not_found()

        context = {
            'proposal': proposal,
            'csrf_token': request.csrf_token(),
            'min_votes_required': 150,
            'min_votes_message': 'A proposal minimum votes must have at least 150 votes.',
            'end_date_required': 30,
            'end_date_message': 'A proposal end date must be at least 30 days after the create date.',
        }
        return request.render('cep_proposal.proposals_edit', context)

    @http.route(['/proposals/update/<int:id>'], type='http', auth='user', website=True)
    def update(self, id, **kwargs):
        if not isAuthenticate():
            return request.redirect('/web/login')

        # Fetch and validate proposal
        proposal = request.env['cep.proposal.proposal'].sudo().browse(int(id))
        if not proposal.exists() or not isProposalOwner(proposal.owner_id):
            return request.redirect('/my/proposals')

        # Handle Cover Photo
        delete_cover_photo = kwargs.pop('delete_cover_photo', None)
        if delete_cover_photo == 'yes':
            kwargs['cover_photo'] = False
        elif 'cover_photo' in kwargs and kwargs['cover_photo']:
            kwargs['cover_photo'] = base64.b64encode(
                kwargs['cover_photo'].read())
        else:
            kwargs['cover_photo'] = proposal.cover_photo

        # Handle Attachments Input
        attachments = request.httprequest.files.getlist(
            'attachment') if kwargs.get('attachment') else []
        kwargs.pop('attachment', None)

        # Handle Tags
        all_tags = kwargs.pop('all_tags', '[]')
        try:
            tags = json.loads(all_tags)
        except json.JSONDecodeError:
            tags = []

        # Handle Attachment Deletion
        delete_attachment_ids = kwargs.pop('delete_attachment_ids', '[]')
        try:
            delete_ids = json.loads(delete_attachment_ids)
        except json.JSONDecodeError:
            delete_ids = []

        if delete_ids:
            attachments_to_delete = request.env['ir.attachment'].sudo().browse(
                delete_ids)
            for attachment in attachments_to_delete:
                if attachment.res_model == 'cep.proposal.proposal' and attachment.res_id == proposal.id:
                    attachment.unlink()

        # Write Proposal Fields
        proposal.write(kwargs)

        # Create and Link New Attachments
        attachment_ids = []
        for attachment in attachments:
            attachment_data = {
                'name': attachment.filename,
                'datas': base64.b64encode(attachment.read()),
                'res_model': 'cep.proposal.proposal',
                'res_id': proposal.id,
            }
            new_attachment = request.env['ir.attachment'].sudo().create(
                attachment_data)
            attachment_ids.append(new_attachment.id)

        if attachment_ids:
            proposal.write(
                {'attachment_ids': [(4, att_id) for att_id in attachment_ids]})

        # Handle Tags Creation and Linking
        if tags:
            tag_ids = []
            for tag_name in tags:
                tag = request.env['cep.proposal.tag'].sudo().search(
                    [('name', '=', tag_name)], limit=1)
                if not tag:
                    tag = request.env['cep.proposal.tag'].sudo().create({
                        'name': tag_name,
                        'owner_id': request.env.user.id
                    })
                tag_ids.append(tag.id)
            proposal.write({'tag_ids': [(6, 0, tag_ids)]})

        # Redirect to Proposal View
        return request.redirect(f'/proposals/{proposal.id}/view')

    @http.route(['/proposals/<string:id>/view'], type='http', auth='public', website=True)
    def view(self, id, **kwargs):
        proposal = request.env['cep.proposal.proposal'].browse(int(id)).sudo()

        # Get the filter type from the query parameters
        # Default to 'newest' if no filter is provided
        filter_type = kwargs.get('filter', 'newest')

        comment_count = len(proposal.get_parent_comments())
        context = {
            'proposal': proposal,
            'user': request.env.user,
            'isProposalOwner': isProposalOwner(proposal.owner_id),
            'comment_count': comment_count,
            'filterType': filter_type,
            'isVotingAllowed': datetime.now().date() <= proposal.end_date,
        }
        return request.render('cep_proposal.proposals_view', context)

    @http.route(['/proposals/<string:id>/delete'], type='http', auth='user', website=True)
    def delete_proposal(self, id, **kwargs):
        proposal = request.env['cep.proposal.proposal'].browse(int(id)).sudo()

        if not isProposalOwner(proposal.owner_id):
            return request.redirect('/web/login')

        proposal.unlink()
        return request.redirect('/my/proposals')

    # Get proposal list by page number API
    @http.route('/proposals/proposal-list/<int:page_no>/<int:per_page>', type='json', auth='public', methods=['POST'], website=True)
    def get_proposal_list_by_page(self, page_no, per_page, **kwargs):
        offset = (page_no - 1) * per_page
        if offset < 0:
            offset = 0
        proposals = request.env['cep.proposal.proposal'].sudo().search(
            [], offset=offset, limit=per_page)
        proposal_list = []
        for proposal in proposals:
            proposal_object = {
                "proposal_id": proposal.id,
                "proposal_title": proposal.title,
                "proposal_description": proposal.description[:48],
                "proposal_cover_photo": proposal.cover_photo.decode("utf-8") if proposal.cover_photo else "",
                "proposal_created_by": proposal.owner_id.name,
                # DD MMM YYYY (01 Jan 2020)
                "proposal_created_date": proposal.create_date.strftime("%d %b %Y"),
                # DD MMM YYYY (01 Jan 2020)
                "proposal_end_date": proposal.end_date.strftime("%d %b %Y"),
                "proposal_votes": proposal.count_vote(),
            }
            proposal_list.append(proposal_object)

        # get number of proposals
        proposal_count = request.env['cep.proposal.proposal'].sudo().search_count([
        ])

        total_pages = (proposal_count + per_page - 1) // per_page

        return {
            "status": "success",
            "proposals": proposal_list,
            "proposal_count": proposal_count,
            "total_pages": total_pages,
        }

    # Proposal Search API
    @http.route('/proposals/search', type='json', auth='public', methods=['POST'], website=True)
    def problem_search(self, **kwargs):
        keyword = kwargs['keyword']
        proposals = request.env['cep.proposal.proposal'].sudo().search([
            '|',
            ('title', 'ilike', keyword),
            ('description', 'ilike', keyword)
        ])
        proposal_list = []
        for proposal in proposals:
            proposal_object = {
                "proposal_id": proposal.id,
                "proposal_title": proposal.title,
                "proposal_description": proposal.description[:48],
                "proposal_cover_photo": proposal.cover_photo.decode("utf-8") if proposal.cover_photo else "",
                "proposal_created_by": proposal.owner_id.name,
                # DD MMM YYYY (01 Jan 2020)
                "proposal_created_date": proposal.create_date.strftime("%d %b %Y"),
                # DD MMM YYYY (01 Jan 2020)
                "proposal_end_date": proposal.end_date.strftime("%d %b %Y"),
                "proposal_votes": proposal.count_vote(),
            }
            proposal_list.append(proposal_object)

        # get number of proposals
        proposal_count = len(proposal_list)

        return {
            "status": "success",
            "proposals": proposal_list,
            "proposal_count": proposal_count
        }

    # Proposal Search suggestions API
    @http.route('/proposals/search_suggestions', type='json', auth='public', methods=['POST'], website=True)
    def proposal_suggestions(self, **kwargs):
        keyword = kwargs['keyword']
        proposals = request.env['cep.proposal.proposal'].sudo().search([
            '|',
            ('title', 'ilike', keyword),
            ('description', 'ilike', keyword),
        ])

        proposal_list = []
        for proposal in proposals:
            proposal_object = {
                "proposal_id": proposal.id,
                "proposal_title": proposal.title,
            }
            proposal_list.append(proposal_object)

        return {
            "status": "success",
            "proposals": proposal_list,
        }


class CommentController(http.Controller):

    @http.route(['/proposals/<string:id>/comments/<string:parent_id>/create'], type='http', auth='user', website=True)
    def create(self, id, parent_id, **kwargs):
        if not isAuthenticate():
            return request.redirect('/web/login')
        kwargs['proposal_id'] = int(id)
        kwargs['owner_id'] = request.env.user.id
        if parent_id != 'None':
            kwargs['parent_id'] = int(parent_id)

        request.env['cep.proposal.comment'].sudo().create(kwargs)
        return request.redirect('/proposals/{}/view'.format(id))

    @http.route(['/proposal-comment/<string:id>/delete'], type='json', methods=['POST'], auth='user', website=True)
    def delete(self, id, **kwargs):
        if not isAuthenticate():
            return {'error': 'not_authenticated', 'redirect_url': '/web/login'}

        try:
            comment = request.env['cep.proposal.comment'].browse(
                int(id)).sudo()
            if comment:
                comment.unlink()
                return {'success': True}
            else:
                return {'error': 'Comment not found'}
        except Exception as e:
            return {'error': str(e)}


class OfficialUpdateController(http.Controller):

    @http.route(['/proposals/<string:id>/official-update/create'], type='http', auth='user', website=True)
    def create(self, id, **kwargs):
        kwargs['proposal_id'] = int(id)
        kwargs['owner_id'] = request.env.user.id

        request.env['cep.proposal.official.update'].sudo().create(kwargs)
        return request.redirect('/proposals/{}/view'.format(id))


class VoteController(http.Controller):

    @http.route(['/proposals/<string:id>/vote/create'], type='http', auth='user', website=True)
    def create(self, id, **kwargs):
        if not isAuthenticate():
            return request.redirect('/web/login')

        kwargs['proposal_id'] = int(id)
        kwargs['owner_id'] = request.env.user.id

        request.env['cep.proposal.vote'].sudo().create(kwargs)
        return request.redirect('/proposals/{}/view'.format(id))

    @http.route(['/proposals/<string:id>/vote/cancel'], type='http', auth='user', website=True)
    def cancel(self, id, **kwargs):
        if not isAuthenticate():
            return request.redirect('/web/login')

        # delete vote
        vote = request.env['cep.proposal.vote'].sudo().search(
            [('proposal_id', '=', int(id)), ('owner_id', '=', request.env.user.id)])
        vote.unlink()
        return request.redirect('/proposals/{}/view'.format(id))


class ReactionController(http.Controller):

    @http.route(['/proposal-comment/<string:id>/reactions/<string:type>/create'], type='json', methods=['POST'], auth='public', website=True)
    def create(self, id, type, **kwargs):
        if not request.session.uid:
            return {
                "error": "not_authenticated",
                "redirect_url": "/web/login"
            }

        kwargs['type'] = str(type)
        kwargs['owner_id'] = request.env.user.id
        kwargs['comment_id'] = int(id)
        reaction = request.env['cep.proposal.reaction'].sudo().search(
            [('type', '=', kwargs['type']), ('comment_id', '=', kwargs['comment_id']), ('owner_id', '=', kwargs['owner_id'])])
        if len(reaction) > 0:
            pass
        else:
            if kwargs['type'] == 'like':
                dislike = request.env['cep.proposal.reaction'].sudo().search([('type', '=', 'dislike'), (
                    'comment_id', '=', kwargs['comment_id']), ('owner_id', '=', kwargs['owner_id'])], limit=1)
                if dislike:
                    dislike.unlink()
            else:
                like = request.env['cep.proposal.reaction'].sudo().search([('type', '=', 'like'), (
                    'comment_id', '=', kwargs['comment_id']), ('owner_id', '=', kwargs['owner_id'])], limit=1)
                if like:
                    like.unlink()
            request.env['cep.proposal.reaction'].sudo().create(kwargs)

        likes_count = request.env['cep.proposal.reaction'].sudo().search_count(
            [('type', '=', 'like'), ('comment_id', '=', kwargs['comment_id'])])
        dislikes_count = request.env['cep.proposal.reaction'].sudo().search_count(
            [('type', '=', 'dislike'), ('comment_id', '=', kwargs['comment_id'])])

        return {
            "likes_count": likes_count,
            "dislikes_count": dislikes_count
        }
