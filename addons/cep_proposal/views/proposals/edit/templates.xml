<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="cep_proposal.proposals_edit" name="Create Proposal">
        <t t-call="website.layout">
            <t t-set="title">Proposal Submit</t>
            <t t-set="head">
                <t t-call-assets="web.assets_common" />
                <t t-call-assets="web.assets_frontend" />
                <t t-call-assets="cep_proposal.proposals_new" />
                <t t-call-assets="cep_proposal.cep_proposals_common" />
            </t>
            <br />
            <div class="oe_structure">
                <div class="container s_proposal_submit">
                    <!-- Go Back Button -->
                    <div class="row justify-content-md-center">
                        <div class="col-12 col-md-6 col-md-auto">
                            <div class="d-flex flex-row align-items-center gap-2 mb-2">
                                <a href="#" class="text-decoration-none back-btn">
                                    <i class="fa fa-angle-left" aria-hidden="true"></i>
                                </a>

                                <h5 class="fw-500 c-dark-gunmetal p-0 m-0">Go Back</h5>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Form Section-->
                    <div class="row justify-content-md-center">
                        <div class="col-12 col-md-6 col-md-auto">
                            <div class="d-flex flex-column align-items-center gap-3 mb-3">
                                <h3 class="fw-800 c-maximum-blue-green">Update Proposal</h3>
                                <form role="form"
                                    t-att-action="'/proposals/update/%d' % proposal.id"
                                    method="POST"
                                    enctype="multipart/form-data" id="proposal_update_form"
                                    class="needs-validation">
                                    <!-- Hidden fields -->
                                    <input type="hidden" name="csrf_token"
                                        t-att-value="request.csrf_token()" />
                                    <input type="hidden" name="all_tags" id="tag_ids_input" value="" />
                                    <textarea class="d-none" name="description" id="description"
                                        value=""></textarea>

                                    <!-- Title and description field -->
                                    <div class="card w-100 mt-4">
                                        <div class="card-body">
                                            <h4 class="fw-700 c-maximum-blue-green">What is your
                                                Proposal?</h4>
                                            <div class="mb-3">
                                                <label for="title" class="form-label">Title</label>
                                                <input type="text" class="form-control" name="title"
                                                    id="title"
                                                    t-att-value="proposal.title" placeholder="Title"
                                                    required="true" />
                                            </div>

                                            <p class="text-form float-end"><span
                                                    id="description-char-numbers">00</span> /600</p>

                                            <div class="form-text mb-2">
                                                Proposal Description
                                            </div>

                                            <div class="mb-3">
                                                <div id="description_editor"
                                                    style="min-height: 100px">
                                                    <t t-esc="proposal.description" />
                                                </div>
                                            </div>

                                        </div>
                                    </div>

                                    <!-- Location, End Date & Minimum Vote -->
                                    <div class="card w-100 mt-4">
                                        <div class="card-body">
                                            <h4 class="fw-700 c-maximum-blue-green">Location, End
                                                Date and Minimum Vote</h4>
                                            <div class="mb-3">
                                                <label for="location" class="form-label">Location</label>
                                                <input type="text" name="location"
                                                    t-att-value="proposal.location"
                                                    id="location" class="form-control"
                                                    required="true" />
                                            </div>

                                            <div class="mb-3">
                                                <label for="min_votes" class="form-label">Minimum
                                                    Vote</label>
                                                <input type="number" name="min_votes"
                                                    t-att-value="proposal.min_votes"
                                                    id="min_votes" class="form-control"
                                                    required="true"
                                                    t-att-min="min_votes_required"
                                                    t-att-data-min="min_votes_required" />
                                                <div class="invalid-feedback">
                                                    <t t-esc="min_votes_message" />
                                                </div>
                                            </div>

                                            <div class="mb-3">
                                                <label for="end_date" class="form-label">End Date</label>
                                                <input type="date" class="form-control"
                                                    id="end_date"
                                                    t-att-value="proposal.end_date" name="end_date"
                                                    required="true"
                                                    t-att-data-create_date="proposal.create_date"
                                                    t-att-data-end_date_required="end_date_required" />
                                                <div class="invalid-feedback">
                                                    <t t-esc="end_date_message" />
                                                </div>
                                            </div>

                                        </div>
                                    </div>

                                    <!-- Attachments and Cover Photo -->
                                    <div class="card w-100 mt-4">
                                        <div class="card-body d-flex flex-column gap-3">
                                            <h4 class="fw-700 c-maximum-blue-green">Attachments</h4>

                                            <div class="d-flex flex-column gap-1 mb-3">
                                                <h6>Cover Photo<span class="fw-400 opacity-80">
                                                    (optional)</span></h6>
                                                <p class="fw-400">Make your proposal stand out. This
                                                    image will be shown
                                                    at the top of the content.</p>

                                                <div
                                                    onclick="document.getElementById('cover_photo').click();"
                                                    class="d-flex flex-column align-items-center gap-3 py-4 upload-image">
                                                    <t t-call="cep_proposal.svg_icon">
                                                        <t t-set="icon_name" t-value="'download'" />
                                                    </t>
                                                    <p class="p-0 m-0">Select an image (max. 10MB)</p>
                                                    <input type="file" name="cover_photo"
                                                        id="cover_photo"
                                                        class="d-none" accept=".jpg, .jpeg, .png" />

                                                </div>
                                                <!-- Preview section -->
                                                <div id="cover_photo_preview"
                                                    class="d-flex flex-wrap gap-2">
                                                    <div t-if="proposal.cover_photo"
                                                        class="position-relative">
                                                        <img
                                                            t-att-src="image_data_uri(proposal.cover_photo)"
                                                            class="img-thumbnail" />
                                                        <button type="button"
                                                            class="btn btn-sm btn-danger position-absolute top-0 start-100 translate-middle remove-file-btn"
                                                            data-file-type="cover_photo"><![CDATA[&times;]]>
                                                        </button>
                                                    </div>
                                                </div>
                                                <input type="hidden" name="delete_cover_photo"
                                                    id="delete_cover_photo_input" />
                                            </div>

                                            <div class="d-flex flex-column gap-1">
                                                <h6>Attachments<span class="fw-400 opacity-80">
                                                    (optional)</span></h6>
                                                <p class="fw-400">Upload files to give others more
                                                    information and
                                                    context</p>

                                                <div
                                                    onclick="document.getElementById('attachment').click();"
                                                    class="d-flex flex-row gap-3 py-3 px-3 upload-image">
                                                    <i class="fa fa-cloud-upload" aria-hidden="true"></i>
                                                    <p class="p-0 m-0">Click to select a file</p>
                                                    <input type="file" name="attachment"
                                                        id="attachment" class="d-none"
                                                        multiple="multiple"
                                                        accept=".doc,.docx,.pdf,image/*" />
                                                </div>
                                            </div>
                                            <!-- Preview section -->
                                            <div id="attachment_preview"
                                                class="d-flex flex-wrap gap-2"></div>


                                            <h6 class='my-0'>Download existing attachment</h6>
                                            <div class="d-flex flex-wrap gap-2">
                                                <t t-foreach="proposal.attachment_ids"
                                                    t-as="attachment">
                                                    <div class="position-relative d-inline-block">
                                                        <a
                                                            t-att-href="'/web/content/' + str(attachment.id) + '?download=true'"
                                                            class="btn btn-info">
                                                            <i class="fa fa-download"></i> Download <t
                                                                t-esc="attachment.name" />
                                                        </a>
                                                        <button type="button"
                                                            class="btn btn-danger btn-sm position-absolute top-0 translate-middle remove-attachment-btn"
                                                            t-att-data-attachment-id="attachment.id"
                                                            data-file-type="attachment"
                                                            title="Remove"><![CDATA[&times;]]>
                                                        </button>
                                                    </div>
                                                </t>
                                            </div>
                                            <input type="hidden" name="delete_attachment_ids"
                                                id="delete_attachment_ids" value="[]" />
                                        </div>
                                    </div>

                                    <div class="card w-100 mt-4">
                                        <div class="card-body">
                                            <div class="d-flex flex-column align-items-start gap-1">
                                                <h4 class="fw-700 c-maximum-blue-green">Use Tags</h4>
                                                <h6>Tags <span class="fw-400">(optional)</span></h6>
                                                <div id="tagContainer" class="tags-container w-100">
                                                    <t t-foreach="proposal.tag_ids" t-as="tag">
                                                        <div class="tag">
                                                            <t t-esc="tag.name" />
                                                            <span class="close-btn"><![CDATA[&times;]]>
                                                    </span>
                                                        </div>
                                                    </t>
                                                    <input type="text" id="tag-input"
                                                        class="form-control"
                                                        placeholder="Type something and press enter" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <button type="submit" class="btn btn-submit mt-4 mb-4">Update
                                        Proposal</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <br />
        </t>
    </template>
</odoo>