<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="cep_proposal.proposals_view" name="Proposal">
        <t t-call="website.layout">
            <t t-set="title">Proposal Details</t>
            <t t-set="head">
                <t t-call-assets="web.assets_common" />
                <t t-call-assets="web.assets_frontend" />
                <t t-call-assets="cep_proposal.cep_proposals_common" />
                <t t-call-assets="cep_proposal.proposals_view" />
            </t>
            <div class="oe_structure">
                <section class="s_proposal_details pt-3 bg-white">
                    <div class="container">
                        <div class="row my-4">

                            <div class="col-12 col-md-8">
                                <div class="d-flex flex-column gap-4 mb-3">
                                    <t t-if="proposal.cover_photo">
                                        <div class="featured-image"
                                            t-attf-style="background-image: url('{{ image_data_uri(proposal.cover_photo) }}');"
                                            alt="proposal cover"></div>
                                    </t>
                                    <t t-else="">
                                        <div class="featured-image"
                                            t-attf-style="background-image: url('/cep_idea/static/src/img/placeholder.png');"
                                            alt="proposal cover"></div>
                                    </t>
                                    <div class="d-flex flex-row align-items-center gap-2">
                                        <t t-call="cep_proposal.svg_icon">
                                            <t t-set="icon_name" t-value="'user'" />
                                        </t>
                                        <div class="d-flex flex-column">
                                            <div
                                                class="d-flex flex-column flex-md-row align-items-md-center gap-2">
                                                <span class="author">Proposed by <t
                                                        t-esc="proposal.owner_id.name" /> - </span>
                                                <span class="date">
                                                    <t
                                                        t-esc="proposal.create_date.strftime('%d %b %Y')" />
                                                </span>
                                            </div>
                                            <div class="d-flex flex-row align-items-center gap-2">
                                                <t t-call="cep_proposal.svg_icon">
                                                    <t t-set="icon_name" t-value="'map'" />
                                                </t>
                                                <span class="location">
                                                    <t t-esc="proposal.location" />
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <h3 class="fw-700 c-dark-gunmetal">
                                        <t t-esc="proposal.title" />
                                    </h3>

                                    <!-- <p class="fw-500 text-justify">
                                        <t t-esc="proposal.description"/>
                                    </p> -->
                                    <div class="text-justify">
                                        <t t-raw="proposal.description" />
                                    </div>
                                </div>

                                <t t-if="proposal.attachment_ids">
                                    <div class='mb-4'>
                                        <h6 class='my-0'>Download Attachments</h6>

                                        <t t-foreach="proposal.attachment_ids" t-as="attachment">
                                            <a
                                                t-att-href="'/web/content/' + str(attachment.id) + '?download=true'"
                                                class="d-block mt-2 ">
                                                <i class="fa fa-download"></i> Download <t
                                                    t-esc="attachment.name" />
                                            </a>
                                        </t>
                                    </div>
                                </t>

                                <!-- official update section start -->
                                <t t-if="proposal.last_official_update()">
                                    <t t-set="official_update"
                                        t-value="proposal.last_official_update()" />
                                    <div class="d-flex flex-column official-update">
                                        <h4 class="fw-700 c-red">Official Update</h4>

                                        <div class="alert alert-seashell p-5" role="alert">
                                            <p>
                                                <t t-esc="official_update.message" />
                                            </p>
                                            <p class="mb-0 date">Posted on - <t
                                                    t-esc="official_update.format_create_date()" /></p>
                                        </div>
                                    </div>
                                </t>

                                <t t-if="isProposalOwner">
                                    <!-- Modal -->
                                    <div class="modal fade" id="confirmationModal" tabindex="-1"
                                        aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title">Confirm Publication</h5>
                                                    <button type="button" class="btn-close"
                                                        data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body"> Are you sure you want to
                                                    publish this official update? This action will <strong>replace
                                                    the current message</strong> and cannot be
                                                    undone. </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary"
                                                        data-bs-dismiss="modal">Cancel</button>
                                                    <button type="button" class="btn btn-primary"
                                                        id="confirmPublish">Yes, Publish</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-flex flex-column official-update">
                                        <h4 class="fw-700 c-red">Add an Official Update</h4>

                                        <form class="d-flex flex-column gap-3"
                                            t-att-action="'official-update/create'"
                                            method="POST">
                                            <input type="hidden" name="csrf_token"
                                                t-att-value="request.csrf_token()" />
                                            <div class="form-floating">
                                                <textarea rows="6" class="form-control"
                                                    placeholder="Message"
                                                    id="official-update-message" name="message"></textarea>
                                                <label for="official-update-message">Message</label>
                                            </div>

                                            <div class="d-flex flex-row gap-2">
                                                <button type="submit" class="btn btn-post">Publish</button>
                                            </div>

                                        </form>
                                    </div>
                                </t>

                                <div class="d-flex flex-column my-4">
                                    <div
                                        class="d-flex flex-column flex-md-row align-items-md-center justify-content-md-between gap-3">
                                        <h3 class="fw-700 c-dark-gunmetal">Comments (<t
                                                t-esc="comment_count" />)</h3>
                                        <div class="dropdown">
                                            <button class="btn btn-md btn-filter dropdown-toggle"
                                                type="button"
                                                id="dropdownMenuButton1" data-bs-toggle="dropdown"
                                                aria-expanded="false">
                                                <t t-out="filterType" />
                                            </button>
                                            <ul class="dropdown-menu"
                                                aria-labelledby="dropdownMenuButton1">
                                                <li>
                                                    <a class="dropdown-item" href="?filter=newest">
                                                        Newest</a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="?filter=oldest">
                                                        Oldest</a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="?filter=all">All</a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                <!-- Main Comment Form -->
                                <div class="d-flex flex-row my-4">
                                    <t t-call="cep_proposal.svg_icon">
                                        <t t-set="icon_name" t-value="'user_sm'" />
                                    </t>

                                    <form class="d-flex flex-column flex-grow-1 ms-2 gap-2"
                                        t-att-action="'comments/' + 'None' + '/create'"
                                        method="POST">
                                        <div class="form-floating">
                                            <input type="hidden" name="csrf_token"
                                                t-att-value="request.csrf_token()" />
                                            <textarea name="message" t-att-value="message"
                                                id="comment" rows="6"
                                                class="form-control"
                                                placeholder="Write your comment here ..."
                                                required="required"></textarea>
                                            <label for="comment">Write your comment here ...</label>
                                        </div>

                                        <div class="d-flex flex-row gap-2">
                                            <button type="submit" class="btn btn-post">Post</button>
                                        </div>

                                    </form>
                                </div>

                                <div id="comments" class="d-flex flex-column gap-3">
                                    <!-- Comments -->
                                    <t t-foreach="proposal.get_parent_comments(filterType)"
                                        t-as="comment">
                                        <div class="d-flex flex-row mb-2"
                                            t-attf-id="comment-#{comment.id}">
                                            <t t-call="cep_proposal.svg_icon">
                                                <t t-set="icon_name" t-value="'user_sm'" />
                                            </t>

                                            <div class="d-flex flex-column flex-grow-1 ms-2 gap-2">
                                                <div class="d-flex flex-row justify-content-between">
                                                    <div
                                                        class="d-flex flex-column flex-md-row gap-2">
                                                        <span class="author">
                                                            <t t-esc="comment.owner_id.name" />
                                                        </span>
                                                        <span class="date">
                                                            <t
                                                                t-esc="comment.create_date.strftime('%d %b %Y')" />
                                                        </span>
                                                    </div>
                                                    <t t-if="uid == comment.owner_id.id">
                                                        <button t-att-data-id="comment.id"
                                                            t-att-data-type="'comment'"
                                                            class="btn delete">
                                                            <i class="fa fa-trash-o"
                                                                aria-hidden="true"></i> <!-- Delete
                                                            icon -->
                                                        </button>
                                                    </t>
                                                </div>
                                                <p class="text-justify">
                                                    <t t-esc="comment.message" />
                                                </p>

                                                <div
                                                    class="reaction-on-comment d-flex flex-row gap-4">
                                                    <button
                                                        t-attf-class="btn reaction_button d-flex flex-row align-items-center text-decoration-none gap-2 #{'liked' if comment.has_user_liked() else ''}"
                                                        t-att-data-model="'proposal-comment'"
                                                        t-att-data-id="comment.id"
                                                        t-att-data-type="'like'">
                                                        <i class="fa fa-thumbs-o-up rounded-circle"
                                                            aria-hidden="true"></i>
                                                        <span> Like(<span
                                                                t-attf-id="likes_count_proposal-comment_#{comment.id}"
                                                                t-esc="comment.count_like()" />) </span>
                                                    </button>

                                                    <button
                                                        t-attf-class="btn reaction_button d-flex flex-row align-items-center text-decoration-none gap-2 #{'disliked' if comment.has_user_disliked() else ''}"
                                                        t-att-data-model="'proposal-comment'"
                                                        t-att-data-id="comment.id"
                                                        t-att-data-type="'dislike'">
                                                        <i
                                                            class="fa fa-thumbs-o-down rounded-circle"
                                                            aria-hidden="true"></i>
                                                        <span> Dislike(<span
                                                                t-attf-id="dislikes_count_proposal-comment_#{comment.id}"
                                                                t-esc="comment.count_dislike()" />) </span>
                                                    </button>
                                                </div>

                                                <!-- Replies -->
                                                <t
                                                    t-foreach="proposal.get_child_comments(comment.id)"
                                                    t-as="reply">
                                                    <div class="d-flex flex-row mb-2"
                                                        t-attf-id="reply-#{reply.id}">
                                                        <i class="fa fa-user-circle-o"
                                                            aria-hidden="true"></i> <!--
                                                        Person icon -->
                                                        <div
                                                            class="d-flex flex-column flex-grow-1 ms-2 gap-2">
                                                            <div
                                                                class="d-flex flex-row justify-content-between">
                                                                <div
                                                                    class="d-flex flex-column flex-md-row gap-2">
                                                                    <span class="author">
                                                                        <t
                                                                            t-esc="reply.owner_id.name" />
                                                                    </span>
                                                                    <span class="date">
                                                                        <t
                                                                            t-esc="reply.create_date.strftime('%d %b %Y')" />
                                                                    </span>
                                                                </div>
                                                                <t t-if="uid == reply.owner_id.id">
                                                                    <button t-att-data-id="reply.id"
                                                                        t-att-data-type="'reply'"
                                                                        class="btn delete">
                                                                        <i class="fa fa-trash-o"
                                                                            aria-hidden="true"></i> <!--
                                                                        Delete icon -->
                                                                    </button>
                                                                </t>
                                                            </div>
                                                            <p class="text-justify">
                                                                <t t-esc="reply.message" />
                                                            </p>

                                                            <div
                                                                class="reaction-on-comment d-flex flex-row gap-4">
                                                                <button
                                                                    t-attf-class="btn reaction_button d-flex flex-row align-items-center text-decoration-none gap-2 #{'liked' if reply.has_user_liked() else ''}"
                                                                    t-att-data-model="'proposal-comment'"
                                                                    t-att-data-id="reply.id"
                                                                    t-att-data-type="'like'">
                                                                    <i
                                                                        class="fa fa-thumbs-o-up rounded-circle"
                                                                        aria-hidden="true"></i>
                                                                    <span>Like(<span
                                                                            t-attf-id="likes_count_proposal-comment_#{reply.id}"
                                                                            t-esc="reply.count_like()" />
                                                                        )</span>
                                                                </button>
                                                                <button
                                                                    t-attf-class="btn reaction_button d-flex flex-row align-items-center text-decoration-none gap-2 #{'disliked' if reply.has_user_disliked() else ''}"
                                                                    t-att-data-model="'proposal-comment'"
                                                                    t-att-data-id="reply.id"
                                                                    t-att-data-type="'dislike'">
                                                                    <i
                                                                        class="fa fa-thumbs-o-down rounded-circle"
                                                                        aria-hidden="true"></i>
                                                                    <span>Dislike(<span
                                                                            t-attf-id="dislikes_count_proposal-comment_#{reply.id}"
                                                                            t-esc="reply.count_dislike()" />
                                                                        )</span>
                                                                </button>
                                                            </div>

                                                        </div>
                                                    </div>
                                                </t>

                                                <!-- Reply Form -->
                                                <div class="d-flex flex-row mb-2">
                                                    <i class="fa fa-user-circle-o"
                                                        aria-hidden="true"></i> <!-- Person
                                                    icon for reply form -->
                                                    <form
                                                        class="d-flex flex-column flex-grow-1 ms-2 gap-2"
                                                        t-att-action="'comments/' + str(comment.id) + '/create'"
                                                        method="POST">
                                                        <input type="hidden" name="csrf_token"
                                                            t-att-value="request.csrf_token()" />
                                                        <div class="form-floating">
                                                            <textarea id="comment"
                                                                class="form-control" name="message"
                                                                placeholder="Write your reply here ..."
                                                                rows="4"></textarea>
                                                            <label for="comment">Write your reply
                                                                here ...</label>
                                                        </div>

                                                        <div class="d-flex flex-row gap-2">
                                                            <button type="submit"
                                                                class="btn btn-post">Post</button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>

                                        </div>
                                    </t>
                                </div>
                            </div>

                            <div class="col-12 col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        Status
                                    </div>
                                    <div class="card-body">
                                        <div class="d-flex flex-column gap-2">
                                            <div class="d-flex flex-row gap-2">
                                                <t t-call="cep_proposal.svg_icon">
                                                    <t t-set="icon_name" t-value="'clock'" />
                                                </t>
                                                <span class="summary">
                                                    <t t-esc="proposal.get_status()" />
                                                </span>
                                            </div>
                                            <t t-if="proposal.get_status() == 'ongoing'">
                                                <p>This proposal is open for vote.</p>
                                            </t>
                                            <t t-if="proposal.get_status() == 'passed'">
                                                <p>This proposal passed as it reached minimum votes
                                                    in time.</p>
                                            </t>
                                            <t t-if="proposal.get_status() == 'expired'">
                                                <p>This proposal expired as it didn't reach minimum
                                                    votes in time.</p>
                                            </t>

                                            <div class="d-flex flex-row justify-content-between">
                                                <span><t t-esc="proposal.count_vote()" /> votes</span>
                                                <span>
                                                    <t t-esc="proposal.min_votes" />
                                                </span>
                                            </div>

                                            <div class="progress">
                                                <t t-set="rounded_vote_percentage"
                                                    t-value="(proposal.count_vote() / proposal.min_votes) * 100" />
                                                <div class="progress-bar" role="progressbar"
                                                    t-attf-style="width: #{rounded_vote_percentage}%"
                                                    aria-valuenow="25"
                                                    aria-valuemin="0" aria-valuemax="100"></div>
                                            </div>

                                            <t t-set="user_has_voted" t-value="False" />
                                            <t t-foreach="proposal.vote_ids" t-as="vote">
                                                <t t-if="vote.owner_id.id == request.env.user.id">
                                                    <t t-set="user_has_voted" t-value="True" />
                                                </t>
                                            </t>
                                            <t t-if="isVotingAllowed">
                                                <t t-if="user_has_voted">
                                                    <a
                                                        t-attf-href="/proposals/#{proposal.id}/vote/cancel"
                                                        class="btn btn-post d-flex flex-row justify-content-center align-items-center gap-2">
                                                        <t t-call="cep_proposal.svg_icon">
                                                            <t t-set="icon_name" t-value="'cross'" />
                                                        </t>
                                                        <span>Cancel Vote</span>
                                                    </a>
                                                </t>
                                                <t t-else="">
                                                    <a
                                                        t-attf-href="/proposals/#{proposal.id}/vote/create"
                                                        class="btn btn-post d-flex flex-row justify-content-center align-items-center gap-2">
                                                        <i class="fa fa-check"></i>
                                                        <span>Vote</span>
                                                    </a>
                                                </t>
                                            </t>
                                            <t t-else="">
                                                <div
                                                    class="alert alert-secondary d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center gap-2 mt-3">
                                                    <div class="d-flex align-items-center gap-2">
                                                        <i class="fa fa-lock text-muted"></i>
                                                        <span>Voting has ended for this proposal. <t
                                                                t-if="proposal.end_date"> Deadline
                                                            was <strong>
                                                                    <t
                                                                        t-esc="proposal.end_date.strftime('%B %d, %Y')" />
                                                                </strong>
                                                            .</t>
                                                        </span>
                                                    </div>
                                                    <span
                                                        class="badge bg-light text-muted text-uppercase small py-1 px-2">
                                                        Voting Closed
                                                    </span>
                                                </div>
                                            </t>

                                        </div>
                                    </div>
                                </div>

                                <div class="card mt-4">
                                    <div class="card-body">
                                        <div class="d-flex align-items-start flex-column">
                                            <h4 class="fw-700 c-maximum-blue-green">Tags</h4>
                                            <div
                                                class="d-flex align-items-start flex-row flex-wrap gap-2">
                                                <t t-foreach="proposal.tag_ids" t-as="tag">
                                                    <span
                                                        class="badge badge-tag py-1 c-dark-gunmetal opacity-100">
                                                        <t t-esc="tag.name" />
                                                    </span>
                                                </t>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="card mt-4">
                                    <div class="card-body">
                                        <div class="d-flex align-items-start flex-column">
                                            <h4 class="fw-700 c-maximum-blue-green">Share</h4>
                                            <div
                                                class="d-flex align-items-start flex-row flex-wrap gap-2">
                                                <a href="#" class="text-decoration-none"
                                                    id="shareFacebook">
                                                    <t t-call="cep_proposal.svg_icon">
                                                        <t t-set="icon_name" t-value="'fb'" />
                                                    </t>
                                                </a>

                                                <a href="#" class="text-decoration-none"
                                                    id="shareTwitter">
                                                    <t t-call="cep_proposal.svg_icon">
                                                        <t t-set="icon_name" t-value="'twitter'" />
                                                    </t>
                                                </a>

                                                <a href="#" class="text-decoration-none"
                                                    id="shareLinkedIn">
                                                    <t t-call="cep_proposal.svg_icon">
                                                        <t t-set="icon_name" t-value="'linkedin'" />
                                                    </t>
                                                </a>

                                                <a href="#" class="text-decoration-none"
                                                    id="shareWhatsApp">
                                                    <t t-call="cep_proposal.svg_icon">
                                                        <t t-set="icon_name" t-value="'whatsapp'" />
                                                    </t>
                                                </a>

                                                <a href="#" class="text-decoration-none"
                                                    id="copyLink">
                                                    <t t-call="cep_proposal.svg_icon">
                                                        <t t-set="icon_name" t-value="'copy_link'" />
                                                    </t>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </t>
    </template>
</odoo>