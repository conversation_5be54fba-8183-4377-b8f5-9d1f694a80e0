# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.
from odoo import models, fields


class Comment(models.Model):
    _name = 'cep.proposal.comment'
    _description = 'CEP Proposal Comment'
    _rec_name = 'message'

    message = fields.Text('Message', required=True, translate=True)
    create_date = fields.Datetime(
        string='Create Date', default=lambda self: fields.Datetime.now(), readonly=True)
    owner_id = fields.Many2one('res.users', string='Owner', index=True,
                               tracking=True, default=lambda self: self.env.user)
    proposal_id = fields.Many2one(
        'cep.proposal.proposal', required=True, ondelete='cascade')
    parent_id = fields.Many2one('cep.proposal.comment', required=False,
                                string='Parent Comment', index=True, ondelete='cascade')
    reaction_ids = fields.One2many(
        'cep.proposal.reaction', 'comment_id', string='Reactions')

    def get_reply_comments(self):
        reply_comments = self.search([('parent_id', '=', self.id)])
        return reply_comments

    def count_like(self):
        like = self.reaction_ids.search(
            [('type', '=', 'like'), ('comment_id', '=', self.id)])
        return len(like)

    def count_dislike(self):
        dislike = self.reaction_ids.search(
            [('type', '=', 'dislike'), ('comment_id', '=', self.id)])
        return len(dislike)

    def has_user_liked(self, user_id=None):
        user_id = user_id or self.env.user.id
        return bool(self.reaction_ids.filtered(lambda r: r.type == 'like' and r.owner_id.id == user_id))

    def has_user_disliked(self, user_id=None):
        user_id = user_id or self.env.user.id
        return bool(self.reaction_ids.filtered(lambda r: r.type == 'dislike' and r.owner_id.id == user_id))
