{
    'name': "CEP Proposal",
    'summary': "Module of CEP Proposal",
    'description': """TODO""",
    'author': "Technovative Solutions LTD",
    'license': "AGPL-3",
    'website': "https://www.technovativesolutions.co.uk",
    'category': 'Advertisement',
    'version': '0.0.1',
    'depends': [
        'web',
        'website'
    ],
    'data': [
        'security/ir.model.access.csv',
        'security/proposal_security.xml',
        'security/vote_security.xml',
        'security/comment_security.xml',
        'security/official_update_security.xml',
        'security/reaction_security.xml',
        'views/cep_proposal_proposal_views.xml',
        'views/cep_proposal_comment_views.xml',
        'views/cep_proposal_official_update_views.xml',
        'views/cep_proposal_vote_views.xml',
        'views/cep_proposal_reaction_views.xml',
        'views/cep_proposal_tag_views.xml',
        'views/proposals/list/templates.xml',
        'views/proposals/new/templates.xml',
        'views/proposals/view/templates.xml',
        'views/proposals/edit/templates.xml',
        'views/snippets/proposal_list.xml',
        'views/snippets/snippets.xml',
        'views/svg_component.xml',
        'views/svg_icons.xml'
    ],
    'assets': {
        'web.assets_frontend': [
            'cep_proposal/static/src/css/cep_proposal_list_snippet.css',
            'cep_proposal/static/src/js/cep_proposal_list_snippet.js',
        ],
        'cep_proposal.proposals_list': [
            'cep_proposal/static/src/css/cep_proposal_list.css',
        ],
        'cep_proposal.proposals_new': [
            'https://cdn.jsdelivr.net/npm/@tinymce/tinymce-webcomponent@2/dist/tinymce-webcomponent.min.js',
            'https://cdn.jsdelivr.net/npm/quill@2.0.2/dist/quill.snow.css',
            'cep_proposal/static/src/css/cep_proposal_submit.css',
            'https://cdn.jsdelivr.net/npm/quill@2.0.2/dist/quill.js',
            'cep_proposal/static/src/js/cep_proposal_submit.js'
        ],
        'cep_proposal.proposals_view': [
            'cep_proposal/static/src/css/cep_proposal_view.css',
            'cep_proposal/static/src/css/cep_proposal_comment.css',
            'cep_proposal/static/src/js/cep_proposal_view.js',
            'cep_proposal/static/src/js/cep_proposal_comment.js'
        ],
        'cep_proposal.cep_proposals_common': [
            'cep_proposal/static/src/css/cep_proposal_common.css',
            'cep_proposal/static/src/js/cep_proposal_common.js'
        ],
    },
    'installable': True,
    'application': True,
    'auto_install': False,
}
